import re
from urllib.parse import quote
from typing import Optional
from config import settings


def is_csdn_image_url(url: str) -> bool:
    """
    判断是否为CSDN图片URL
    
    Args:
        url: 图片URL
        
    Returns:
        bool: 是否为CSDN图片URL
    """
    csdn_domains = [
        'img-blog.csdnimg.cn',
        'img-blog.csdn.net',
        'img.blog.csdn.net',
        'imgconvert.csdnimg.cn',
        'img-home.csdnimg.cn',
        'img-community.csdnimg.cn',
        'img-operation.csdnimg.cn',
        'img-mid.csdnimg.cn',
        'i-blog.csdnimg.cn'
    ]
    
    url_lower = url.lower()
    return any(domain in url_lower for domain in csdn_domains)


def get_image_proxy_url(original_url: str, base_url: str = None) -> str:
    """
    生成图片代理URL
    
    Args:
        original_url: 原始图片URL
        base_url: API基础URL，如果为None则使用默认配置
        
    Returns:
        str: 图片代理URL
    """
    if not base_url:
        # 默认使用本地API地址，实际部署时应该配置为正确的域名
        base_url = "http://localhost:11211"
    
    # 构建图片代理URL
    api_version = getattr(settings.APP, 'API_VERSION', '/api/v1')
    proxy_url = f"{base_url}{api_version}/image-proxy?url={quote(original_url)}"
    
    return proxy_url


def replace_image_urls_in_markdown(markdown_content: str, base_url: str = None) -> str:
    """
    替换Markdown内容中的CSDN图片链接为图片代理地址
    
    Args:
        markdown_content: 原始Markdown内容
        base_url: API基础URL，如果为None则使用默认配置
        
    Returns:
        str: 替换后的Markdown内容
    """
    if not markdown_content:
        return markdown_content
    
    # 匹配Markdown中的图片语法: ![alt](url) 和 ![alt](url "title")
    img_pattern = r'!\[([^\]]*)\]\(([^)]+)\)'
    
    def replace_image(match):
        alt_text = match.group(1)
        original_url = match.group(2).strip()
        
        # 分离URL和title（如果有的话）
        url_parts = original_url.split(' ', 1)
        image_url = url_parts[0].strip('"\'')
        title_part = f' {url_parts[1]}' if len(url_parts) > 1 else ''
        
        # 检查是否为CSDN图片URL
        if is_csdn_image_url(image_url):
            # 替换为图片代理URL
            proxy_url = get_image_proxy_url(image_url, base_url)
            return f'![{alt_text}]({proxy_url}{title_part})'
        else:
            # 不是CSDN图片，保持原样
            return match.group(0)
    
    # 执行替换
    result = re.sub(img_pattern, replace_image, markdown_content)
    
    return result


def extract_image_urls_from_markdown(markdown_content: str) -> list[dict]:
    """
    从Markdown内容中提取所有图片URL信息
    
    Args:
        markdown_content: Markdown内容
        
    Returns:
        list[dict]: 包含图片信息的列表，每个元素包含 alt, url, is_csdn 字段
    """
    if not markdown_content:
        return []
    
    img_pattern = r'!\[([^\]]*)\]\(([^)]+)\)'
    images = []
    
    for match in re.finditer(img_pattern, markdown_content):
        alt_text = match.group(1)
        original_url = match.group(2).strip()
        
        # 分离URL和title
        url_parts = original_url.split(' ', 1)
        image_url = url_parts[0].strip('"\'')
        
        images.append({
            'alt': alt_text,
            'url': image_url,
            'is_csdn': is_csdn_image_url(image_url),
            'full_match': match.group(0)
        })
    
    return images


def get_replacement_stats(original_content: str, processed_content: str) -> dict:
    """
    获取图片替换统计信息
    
    Args:
        original_content: 原始内容
        processed_content: 处理后内容
        
    Returns:
        dict: 统计信息
    """
    original_images = extract_image_urls_from_markdown(original_content)
    processed_images = extract_image_urls_from_markdown(processed_content)
    
    csdn_images_count = sum(1 for img in original_images if img['is_csdn'])
    total_images_count = len(original_images)
    
    return {
        'total_images': total_images_count,
        'csdn_images': csdn_images_count,
        'replaced_images': csdn_images_count,
        'replacement_rate': csdn_images_count / total_images_count if total_images_count > 0 else 0
    }
