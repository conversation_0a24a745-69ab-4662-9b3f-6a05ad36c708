from typing import Optional
from pydantic import BaseModel, Field, validator
from datetime import datetime
from models import ExtractionRecord, ExtractionStatus
from utils.model_pydantic import pydantic_model_creator
import re


# 输出模型（排除敏感信息）
ExtractionRecordOut = pydantic_model_creator(
    ExtractionRecord,
    name="ExtractionRecordOut",
    exclude=(
        'is_deleted',
        'client_ip',  # 敏感信息
        'user_agent',  # 敏感信息
        'access_token',  # 排除关联字段避免验证错误
    )
)

# 管理员查看的完整信息
ExtractionRecordAdmin = pydantic_model_creator(
    ExtractionRecord,
    name="ExtractionRecordAdmin",
    exclude=(
        'is_deleted',
        'access_token',  # 排除关联字段避免验证错误
    )
)

# 简化的输出模型（包含content字段）
ExtractionRecordSimple = pydantic_model_creator(
    ExtractionRecord,
    name="ExtractionRecordSimple",
    exclude=(
        'is_deleted',
        'client_ip',
        'user_agent',
        'error_message',
        'core_response_time',
        'core_server',
        'retry_count',
        'access_token',  # 排除关联字段避免验证错误
    ),
    # 明确指定包含的字段，确保content字段被包含
    include=(
        'id', 'uuid', 'create_time', 'update_time', 'create_by', 'update_by', 'remark',
        'url', 'url_hash', 'title', 'author', 'status',
        'content', 'content_length',  # 明确包含content字段
        'request_time', 'complete_time'
    )
)


class ExtractionRequest(BaseModel):
    """文章提取请求"""
    url: str = Field(..., description="CSDN文章URL")
    token: str = Field(..., description="访问令牌")
    force_refresh: bool = Field(False, description="强制重新提取（忽略缓存）")

    @validator('url')
    def validate_url(cls, v):
        # 验证是否为有效的CSDN文章URL
        csdn_patterns = [
            r'https?://blog\.csdn\.net/\w+/article/details/\d+',
            r'https?://\w+\.blog\.csdn\.net/article/details/\d+',
        ]
        
        if not any(re.match(pattern, v) for pattern in csdn_patterns):
            raise ValueError('请提供有效的CSDN文章URL')
        
        return v

    @validator('token')
    def validate_token(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('访问令牌不能为空')
        return v.strip()

    class Config:
        schema_extra = {
            'example': {
                'url': 'https://blog.csdn.net/username/article/details/123456789',
                'token': 'your_access_token_here',
                'force_refresh': False
            }
        }


class ExtractionResponse(BaseModel):
    """文章提取响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[ExtractionRecordOut] = Field(None, description="提取结果")
    is_duplicate: bool = Field(False, description="是否为重复提取")
    error_code: Optional[str] = Field(None, description="错误代码")


class ExtractionQuery(BaseModel):
    """查询提取记录"""
    status: Optional[ExtractionStatus] = Field(None, description="提取状态")
    access_token_id: Optional[int] = Field(None, description="访问令牌ID")
    url_keyword: Optional[str] = Field(None, description="URL关键词")
    title_keyword: Optional[str] = Field(None, description="标题关键词")
    author: Optional[str] = Field(None, description="作者")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(10, ge=1, le=100, description="每页数量")


class ExtractionStatsRequest(BaseModel):
    """提取统计请求"""
    days: int = Field(7, ge=1, le=365, description="统计天数")
    access_token_id: Optional[int] = Field(None, description="指定令牌ID")


class ExtractionStats(BaseModel):
    """提取统计"""
    total_extractions: int = Field(..., description="总提取次数")
    success_extractions: int = Field(..., description="成功提取次数")
    failed_extractions: int = Field(..., description="失败提取次数")
    duplicate_extractions: int = Field(..., description="重复提取次数")
    today_extractions: int = Field(..., description="今日提取次数")
    success_rate: float = Field(..., description="成功率")
    average_response_time: float = Field(..., description="平均响应时间")
    unique_articles: int = Field(..., description="唯一文章数")
    top_authors: list = Field(..., description="热门作者")
    daily_stats: dict = Field(..., description="每日统计")


class BatchExtractionRequest(BaseModel):
    """批量提取请求"""
    urls: list[str] = Field(..., description="文章URL列表")
    token: str = Field(..., description="访问令牌")
    force_refresh: bool = Field(False, description="强制重新提取")
    
    @validator('urls')
    def validate_urls(cls, v):
        if not v or len(v) == 0:
            raise ValueError('URL列表不能为空')
        
        if len(v) > 10:  # 限制批量处理数量
            raise ValueError('批量处理最多支持10个URL')
        
        # 验证每个URL
        csdn_patterns = [
            r'https?://blog\.csdn\.net/\w+/article/details/\d+',
            r'https?://\w+\.blog\.csdn\.net/article/details/\d+',
        ]
        
        for url in v:
            if not any(re.match(pattern, url) for pattern in csdn_patterns):
                raise ValueError(f'无效的CSDN文章URL: {url}')
        
        return v

    class Config:
        schema_extra = {
            'example': {
                'urls': [
                    'https://blog.csdn.net/username/article/details/123456789',
                    'https://blog.csdn.net/username/article/details/987654321'
                ],
                'token': 'your_access_token_here',
                'force_refresh': False
            }
        }


class BatchExtractionResponse(BaseModel):
    """批量提取响应"""
    success: bool = Field(..., description="整体是否成功")
    message: str = Field(..., description="响应消息")
    total_count: int = Field(..., description="总数量")
    success_count: int = Field(..., description="成功数量")
    failed_count: int = Field(..., description="失败数量")
    duplicate_count: int = Field(..., description="重复数量")
    results: list[ExtractionResponse] = Field(..., description="详细结果")


class ExtractionHistoryRequest(BaseModel):
    """提取历史请求"""
    token: str = Field(..., description="访问令牌")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(10, ge=1, le=50, description="每页数量")
    status: Optional[ExtractionStatus] = Field(None, description="筛选状态")


class ExtractionHistoryResponse(BaseModel):
    """提取历史响应"""
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    pages: int = Field(..., description="总页数")
    items: list[ExtractionRecordSimple] = Field(..., description="历史记录")
