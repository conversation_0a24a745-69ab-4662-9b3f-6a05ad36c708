# -*- coding: utf-8 -*-
# <AUTHOR> zy
# @Time     : 2023/6/13 23:19
# @File     : tztime.py
# @Project  : template


# 获取上海时间
from datetime import datetime
import pytz
import time


def get_sh_tz_time():
    # # 创建时区对象
    # tz = pytz.timezone('Asia/Shanghai')
    # 获取当前时间
    # now = datetime.now(tz)

    # # 获取utc时间
    # utc_dt = datetime.utcnow()

    utc_dt = time.time()

    # 计算与 UTC 时间的偏移量（以秒为单位）
    utc_offset = 8 * 3600

    # 计算本地时间戳
    local_ts = utc_dt + utc_offset

    # 转换为 datetime 对象
    local_dt = datetime.fromtimestamp(local_ts)


    # 输出上海时间
    # print(now)
    return local_dt


def get_sh_tz_time_aware():
    """获取上海时区的当前时间（带时区信息）"""
    # 创建上海时区对象
    tz = pytz.timezone('Asia/Shanghai')
    # 获取当前UTC时间并转换为上海时间
    utc_now = datetime.now(pytz.UTC)
    sh_time = utc_now.astimezone(tz)
    return sh_time
