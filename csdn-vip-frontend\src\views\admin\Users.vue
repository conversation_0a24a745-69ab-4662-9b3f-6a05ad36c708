<template>
  <div class="space-y-6">
    <!-- Page header -->
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">用户管理</h1>
        <p class="text-gray-600">管理系统管理员用户</p>
      </div>
      <button
        @click="showCreateModal = true"
        class="btn btn-primary"
      >
        创建用户
      </button>
    </div>

    <!-- Users table -->
    <div class="card">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                用户信息
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                最后登录
              </th>

              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-if="loading">
              <td colspan="4" class="px-6 py-4 text-center text-gray-500">
                <div class="flex items-center justify-center">
                  <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-primary-600 mr-2"></div>
                  加载中...
                </div>
              </td>
            </tr>
            
            <tr v-else-if="users.length === 0">
              <td colspan="4" class="px-6 py-4 text-center text-gray-500">
                暂无用户记录
              </td>
            </tr>
            
            <tr v-else v-for="user in users" :key="user.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="h-10 w-10 rounded-full bg-primary-600 flex items-center justify-center">
                    <span class="text-white font-medium">
                      {{ user.username.slice(0, 2).toUpperCase() }}
                    </span>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">{{ user.username }}</div>
                    <div class="text-sm text-gray-500">{{ user.email || '未设置邮箱' }}</div>
                    <div v-if="user.nickname" class="text-sm text-gray-500">{{ user.nickname }}</div>
                  </div>
                </div>
              </td>
              
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="space-y-1">
                  <span
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    :class="{
                      'bg-green-100 text-green-800': user.is_active,
                      'bg-red-100 text-red-800': !user.is_active
                    }"
                  >
                    {{ user.is_active ? '活跃' : '禁用' }}
                  </span>
                  <br>
                  <span
                    v-if="user.is_super"
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800"
                  >
                    超级管理员
                  </span>
                </div>
              </td>
              
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ user.last_login_time ? formatDate(user.last_login_time) : '从未登录' }}
              </td>
              

              
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                <button
                  @click="editUser(user)"
                  class="text-indigo-600 hover:text-indigo-900"
                >
                  编辑
                </button>
                <button
                  @click="toggleUserStatus(user)"
                  :class="user.is_active ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'"
                >
                  {{ user.is_active ? '禁用' : '启用' }}
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Create/Edit Modal -->
    <div
      v-if="showCreateModal || editingUser"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
    >
      <div class="bg-white rounded-lg max-w-md w-full p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">
          {{ editingUser ? '编辑用户' : '创建用户' }}
        </h3>
        
        <form @submit.prevent="saveUser" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
            <input
              v-model="userForm.username"
              type="text"
              required
              :disabled="!!editingUser"
              class="input"
              placeholder="用户名"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
            <input
              v-model="userForm.email"
              type="email"
              class="input"
              placeholder="邮箱地址"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">昵称</label>
            <input
              v-model="userForm.nickname"
              type="text"
              class="input"
              placeholder="昵称"
            />
          </div>
          
          <div v-if="!editingUser">
            <label class="block text-sm font-medium text-gray-700 mb-1">密码</label>
            <input
              v-model="userForm.password"
              type="password"
              required
              class="input"
              placeholder="密码"
            />
          </div>
          
          <div v-if="!editingUser">
            <label class="block text-sm font-medium text-gray-700 mb-1">确认密码</label>
            <input
              v-model="userForm.confirm"
              type="password"
              required
              class="input"
              placeholder="确认密码"
            />
          </div>
          
          <div class="flex items-center">
            <input
              v-model="userForm.is_active"
              type="checkbox"
              class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <label class="ml-2 block text-sm text-gray-700">
              启用用户
            </label>
          </div>
          
          <div class="flex items-center">
            <input
              v-model="userForm.is_super"
              type="checkbox"
              class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <label class="ml-2 block text-sm text-gray-700">
              超级管理员
            </label>
          </div>
          
          <div class="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              @click="closeModal"
              class="btn btn-secondary"
            >
              取消
            </button>
            <button
              type="submit"
              :disabled="saving"
              class="btn btn-primary"
            >
              {{ saving ? '保存中...' : '保存' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { adminUserAPI } from '@/services/api'
import type { AdminUser } from '@/types'

const loading = ref(false)
const saving = ref(false)
const users = ref<AdminUser[]>([])
const showCreateModal = ref(false)
const editingUser = ref<AdminUser | null>(null)

const userForm = reactive({
  username: '',
  email: '',
  nickname: '',
  password: '',
  confirm: '',
  is_active: true,
  is_super: false
})

const loadUsers = async () => {
  loading.value = true
  
  try {
    const response = await adminUserAPI.list()
    users.value = response.data.result.data || []
  } catch (error) {
    console.error('Failed to load users:', error)
  } finally {
    loading.value = false
  }
}

const editUser = (user: AdminUser) => {
  editingUser.value = user
  Object.assign(userForm, {
    username: user.username,
    email: user.email || '',
    nickname: user.nickname || '',
    password: '',
    confirm: '',
    is_active: user.is_active,
    is_super: user.is_super
  })
}

const closeModal = () => {
  showCreateModal.value = false
  editingUser.value = null
  Object.assign(userForm, {
    username: '',
    email: '',
    nickname: '',
    password: '',
    confirm: '',
    is_active: true,
    is_super: false
  })
}

const saveUser = async () => {
  if (!editingUser.value && userForm.password !== userForm.confirm) {
    alert('密码确认不匹配')
    return
  }
  
  saving.value = true
  
  try {
    let data: any

    if (editingUser.value) {
      // 编辑用户时，不发送username字段（不允许修改）
      data = {
        email: userForm.email || undefined,
        nickname: userForm.nickname || undefined,
        is_active: userForm.is_active,
        is_super: userForm.is_super
      }
      await adminUserAPI.update(editingUser.value.id, data)
    } else {
      // 创建用户时，需要发送username字段
      data = {
        username: userForm.username,
        email: userForm.email || undefined,
        nickname: userForm.nickname || undefined,
        is_active: userForm.is_active,
        is_super: userForm.is_super,
        ...(userForm.password && { password: userForm.password, confirm: userForm.confirm })
      }
      await adminUserAPI.create(data)
    }
    
    closeModal()
    await loadUsers()
  } catch (error) {
    console.error('Failed to save user:', error)
  } finally {
    saving.value = false
  }
}

const toggleUserStatus = async (user: AdminUser) => {
  const action = user.is_active ? '禁用' : '启用'
  if (!confirm(`确定要${action}用户 "${user.username}" 吗？`)) return
  
  try {
    await adminUserAPI.update(user.id, { is_active: !user.is_active })
    await loadUsers()
  } catch (error) {
    console.error('Failed to toggle user status:', error)
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  loadUsers()
})
</script>
