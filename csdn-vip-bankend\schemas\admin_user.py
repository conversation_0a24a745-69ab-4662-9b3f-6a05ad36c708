from typing import List, Optional

from fastapi import Query
from pydantic import BaseModel, Field
from models import AdminUser
from utils.model_pydantic import pydantic_model_creator
from .base import PasswordBase, BaseListQueryModel

AdminUserOut = pydantic_model_creator(
    AdminUser,
    name="AdminUserOut",
    exclude=('hashed_password', 'create_time', 'update_time')
)

AdminUserCreateBase = pydantic_model_creator(
    AdminUser,
    name="AdminUserCreate",
    exclude=(
        'id', 'uuid', 'hashed_password', 'create_time', 'update_time', 'create_by', 'update_by', 'last_login_time'
    )
)

AdminUserUpdate = pydantic_model_creator(
    AdminUser,
    name="AdminUserUpdate",
    exclude=(
        'id', 'uuid', 'username', 'hashed_password', 'create_time', 'update_time', 'create_by', 'update_by', 'last_login_time'
    )
)


class AdminUserDetail(AdminUserOut):
    user_roles: dict = Field(..., description='用户角色')
    policies: List[list] = Field(..., description='权限列表')


class AdminUserCreate(AdminUserCreateBase, PasswordBase):
    pass


class AdminUserPasswordUpdate(PasswordBase):
    old_password: str = Field(..., description='旧密码')


class AdminUserActiveUpdate(BaseModel):
    is_active: bool = Field(..., description='是否启用')


class AdminUserListQuery(BaseListQueryModel):
    def __init__(
            self,
            limit: Optional[int] = Query(default=None),
            skip: int = Query(default=0),
            uername: Optional[str] = Query(default=None),
            create_by: Optional[str] = Query(default=None)
    ):
        self.username = uername
        self.create_by = create_by
        super().__init__(
            limit=limit,
            skip=skip,
        )
