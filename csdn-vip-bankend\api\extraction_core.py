from models import Extraction<PERSON>ore, AdminUser, CoreStatus
from schemas import (
    ExtractionCoreOut, ExtractionCoreAdmin, ExtractionCoreCreate, ExtractionCoreUpdate,
    ExtractionCoreQuery, ExtractionCoreStats, HealthCheckRequest, HealthCheckResponse,
    LoadBalancingInfo, CorePerformanceMetrics
)
from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from core.auth.base import get_current_active_user
from core.response import ResultResponse
from core.exce.base import UserError
from datetime import datetime, timedelta
import httpx
import asyncio
import time


extraction_core_router = APIRouter(
    prefix="/extraction-core",
    tags=["提取核心管理"],
)


@extraction_core_router.post(
    "",
    name="Create Extraction Core",
    summary="创建提取核心配置",
    response_model=ResultResponse[ExtractionCoreAdmin]
)
async def create_extraction_core(
    core_data: ExtractionCoreCreate,
    current_user: AdminUser = Depends(get_current_active_user)
) -> ResultResponse[ExtractionCoreAdmin]:
    """创建新的提取核心配置"""
    
    # 检查名称是否重复
    existing = await ExtractionCore.get_or_none(name=core_data.name)
    if existing and not existing.is_deleted:
        raise UserError('服务名称已存在')
    
    # 创建核心配置
    core = await ExtractionCore.create(
        name=core_data.name,
        description=core_data.description,
        api_endpoint=core_data.api_endpoint,
        auth_token=core_data.auth_token,
        timeout_seconds=core_data.timeout_seconds,
        max_retries=core_data.max_retries,
        priority=core_data.priority,
        weight=core_data.weight,
        max_concurrent_requests=core_data.max_concurrent_requests,
        health_check_url=core_data.health_check_url,
        created_by=current_user
    )
    
    # 重新获取带关联数据的核心
    core = await ExtractionCore.get_or_none(id=core.id).prefetch_related('created_by', 'updated_by')
    result = ExtractionCoreAdmin.from_orm(core)
    return ResultResponse[ExtractionCoreAdmin](result=result)


@extraction_core_router.get(
    "",
    name="List Extraction Cores",
    summary="获取提取核心列表",
    response_model=ResultResponse[dict]
)
async def list_extraction_cores(
    query: ExtractionCoreQuery = Depends(),
    current_user: AdminUser = Depends(get_current_active_user)
) -> ResultResponse[dict]:
    """获取提取核心列表"""
    
    # 构建查询条件
    filters = {}
    if query.status is not None:
        filters['status'] = query.status
    if query.is_healthy is not None:
        filters['is_healthy'] = query.is_healthy
    
    # 分页查询
    offset = (query.page - 1) * query.page_size
    cores = await ExtractionCore.filter(**filters)\
        .prefetch_related('created_by', 'updated_by')\
        .order_by('priority', 'id')\
        .offset(offset)\
        .limit(query.page_size)\
        .all()
    
    # 获取总数
    total = await ExtractionCore.filter(**filters).count()

    # 计算总页数
    pages = (total + query.page_size - 1) // query.page_size

    results = [ExtractionCoreAdmin.from_orm(core) for core in cores]

    # 返回分页格式的数据
    pagination_result = {
        "items": results,
        "total": total,
        "page": query.page,
        "size": query.page_size,
        "pages": pages
    }

    return ResultResponse[dict](result=pagination_result)


@extraction_core_router.get(
    "/{core_id}",
    name="Get Extraction Core",
    summary="获取提取核心详情",
    response_model=ResultResponse[ExtractionCoreAdmin]
)
async def get_extraction_core(
    core_id: int,
    current_user: AdminUser = Depends(get_current_active_user)
) -> ResultResponse[ExtractionCoreAdmin]:
    """获取提取核心详情"""
    
    core = await ExtractionCore.get_or_none(id=core_id).prefetch_related('created_by', 'updated_by')
    if not core or core.is_deleted:
        raise UserError('提取核心不存在')
    
    result = ExtractionCoreAdmin.from_orm(core)
    return ResultResponse[ExtractionCoreAdmin](result=result)


@extraction_core_router.put(
    "/{core_id}",
    name="Update Extraction Core",
    summary="更新提取核心配置",
    response_model=ResultResponse[ExtractionCoreAdmin]
)
async def update_extraction_core(
    core_id: int,
    core_data: ExtractionCoreUpdate,
    current_user: AdminUser = Depends(get_current_active_user)
) -> ResultResponse[ExtractionCoreAdmin]:
    """更新提取核心配置"""
    
    core = await ExtractionCore.get_or_none(id=core_id)
    if not core or core.is_deleted:
        raise UserError('提取核心不存在')
    
    # 检查名称重复（如果更新了名称）
    if core_data.name and core_data.name != core.name:
        existing = await ExtractionCore.get_or_none(name=core_data.name)
        if existing and not existing.is_deleted and existing.id != core_id:
            raise UserError('服务名称已存在')
    
    # 更新字段
    update_data = core_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(core, field, value)
    
    core.updated_by = current_user
    await core.save()

    # 重新获取带关联数据的核心
    core = await ExtractionCore.get_or_none(id=core_id).prefetch_related('created_by', 'updated_by')
    result = ExtractionCoreAdmin.from_orm(core)
    return ResultResponse[ExtractionCoreAdmin](result=result)


@extraction_core_router.delete(
    "/{core_id}",
    name="Delete Extraction Core",
    summary="删除提取核心配置",
    response_model=ResultResponse[dict]
)
async def delete_extraction_core(
    core_id: int,
    current_user: AdminUser = Depends(get_current_active_user)
) -> ResultResponse[dict]:
    """删除提取核心配置"""
    
    core = await ExtractionCore.get_or_none(id=core_id)
    if not core or core.is_deleted:
        raise UserError('提取核心不存在')
    
    # 软删除
    core.is_deleted = True
    await core.save()
    
    return ResultResponse[dict](result={"message": "提取核心已删除"})


@extraction_core_router.post(
    "/{core_id}/health-check",
    name="Health Check",
    summary="健康检查",
    response_model=ResultResponse[HealthCheckResponse]
)
async def health_check(
    core_id: int,
    request: HealthCheckRequest,
    current_user: AdminUser = Depends(get_current_active_user)
) -> ResultResponse[HealthCheckResponse]:
    """执行健康检查"""
    
    core = await ExtractionCore.get_or_none(id=core_id)
    if not core or core.is_deleted:
        raise UserError('提取核心不存在')
    
    check_time = datetime.now()
    error_message = None
    is_healthy = False
    response_time = 0.0
    
    try:
        # 使用健康检查URL或默认API端点
        check_url = core.health_check_url or core.api_endpoint
        
        start_time = time.time()
        async with httpx.AsyncClient(timeout=request.timeout) as client:
            response = await client.get(check_url)
            response_time = time.time() - start_time
            
            # 检查响应状态
            if response.status_code == 200:
                is_healthy = True
            else:
                error_message = f"HTTP {response.status_code}: {response.text}"
                
    except httpx.TimeoutException:
        error_message = "请求超时"
    except httpx.ConnectError:
        error_message = "连接失败"
    except Exception as e:
        error_message = f"健康检查失败: {str(e)}"
    
    # 更新核心状态
    core.is_healthy = is_healthy
    core.last_health_check = check_time
    if not is_healthy and core.status == CoreStatus.ACTIVE:
        core.status = CoreStatus.ERROR
    elif is_healthy and core.status == CoreStatus.ERROR:
        core.status = CoreStatus.ACTIVE
    
    await core.save()
    
    # 构建响应
    health_response = HealthCheckResponse(
        core_id=core_id,
        is_healthy=is_healthy,
        response_time=response_time,
        error_message=error_message,
        check_time=check_time
    )
    
    return ResultResponse[HealthCheckResponse](result=health_response)


@extraction_core_router.post(
    "/batch-health-check",
    name="Batch Health Check",
    summary="批量健康检查",
    response_model=ResultResponse[List[HealthCheckResponse]]
)
async def batch_health_check(
    background_tasks: BackgroundTasks,
    current_user: AdminUser = Depends(get_current_active_user)
) -> ResultResponse[List[HealthCheckResponse]]:
    """批量执行健康检查"""
    
    # 获取所有活跃的核心
    cores = await ExtractionCore.filter(
        status__in=[CoreStatus.ACTIVE, CoreStatus.ERROR],
        is_deleted=False
    ).all()
    
    if not cores:
        return ResultResponse[List[HealthCheckResponse]](result=[])
    
    # 并发执行健康检查
    async def check_single_core(core: ExtractionCore) -> HealthCheckResponse:
        check_time = datetime.now()
        error_message = None
        is_healthy = False
        response_time = 0.0
        
        try:
            check_url = core.health_check_url or core.api_endpoint
            
            start_time = time.time()
            async with httpx.AsyncClient(timeout=10) as client:
                response = await client.get(check_url)
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    is_healthy = True
                else:
                    error_message = f"HTTP {response.status_code}"
                    
        except Exception as e:
            error_message = str(e)
        
        # 更新核心状态
        core.is_healthy = is_healthy
        core.last_health_check = check_time
        if not is_healthy and core.status == CoreStatus.ACTIVE:
            core.status = CoreStatus.ERROR
        elif is_healthy and core.status == CoreStatus.ERROR:
            core.status = CoreStatus.ACTIVE
        
        await core.save()
        
        return HealthCheckResponse(
            core_id=core.id,
            is_healthy=is_healthy,
            response_time=response_time,
            error_message=error_message,
            check_time=check_time
        )
    
    # 并发执行所有检查
    tasks = [check_single_core(core) for core in cores]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 处理异常结果
    health_responses = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            health_responses.append(HealthCheckResponse(
                core_id=cores[i].id,
                is_healthy=False,
                response_time=0.0,
                error_message=f"检查异常: {str(result)}",
                check_time=datetime.now()
            ))
        else:
            health_responses.append(result)
    
    return ResultResponse[List[HealthCheckResponse]](result=health_responses)


@extraction_core_router.get(
    "/{core_id}/stats",
    name="Get Core Statistics",
    summary="获取核心统计",
    response_model=ResultResponse[ExtractionCoreStats]
)
async def get_core_statistics(
    core_id: int,
    current_user: AdminUser = Depends(get_current_active_user)
) -> ResultResponse[ExtractionCoreStats]:
    """获取提取核心统计信息"""
    
    core = await ExtractionCore.get_or_none(id=core_id)
    if not core or core.is_deleted:
        raise UserError('提取核心不存在')
    
    # 计算利用率
    utilization_rate = 0.0
    if core.max_concurrent_requests > 0:
        utilization_rate = (core.current_requests / core.max_concurrent_requests) * 100
    
    stats = ExtractionCoreStats(
        total_requests=core.total_requests,
        success_requests=core.success_requests,
        failed_requests=core.failed_requests,
        success_rate=core.success_rate,
        average_response_time=core.average_response_time,
        current_requests=core.current_requests,
        max_concurrent_requests=core.max_concurrent_requests,
        utilization_rate=utilization_rate,
        last_health_check=core.last_health_check,
        is_healthy=core.is_healthy
    )
    
    return ResultResponse[ExtractionCoreStats](result=stats)


@extraction_core_router.get(
    "/load-balancing/info",
    name="Get Load Balancing Info",
    summary="获取负载均衡信息",
    response_model=ResultResponse[LoadBalancingInfo]
)
async def get_load_balancing_info(
    current_user: AdminUser = Depends(get_current_active_user)
) -> ResultResponse[LoadBalancingInfo]:
    """获取负载均衡信息"""
    
    # 获取所有核心
    all_cores = await ExtractionCore.filter(is_deleted=False).all()
    available_cores = [core for core in all_cores if core.is_available]
    
    # 计算总体统计
    total_current_requests = sum(core.current_requests for core in all_cores)
    total_capacity = sum(core.max_concurrent_requests for core in all_cores)
    
    # 计算整体利用率
    utilization_rate = 0.0
    if total_capacity > 0:
        utilization_rate = (total_current_requests / total_capacity) * 100
    
    # 生成各核心状态
    cores_status = []
    for core in all_cores:
        core_utilization = 0.0
        if core.max_concurrent_requests > 0:
            core_utilization = (core.current_requests / core.max_concurrent_requests) * 100
        
        core_stats = ExtractionCoreStats(
            total_requests=core.total_requests,
            success_requests=core.success_requests,
            failed_requests=core.failed_requests,
            success_rate=core.success_rate,
            average_response_time=core.average_response_time,
            current_requests=core.current_requests,
            max_concurrent_requests=core.max_concurrent_requests,
            utilization_rate=core_utilization,
            last_health_check=core.last_health_check,
            is_healthy=core.is_healthy
        )
        cores_status.append(core_stats)
    
    load_info = LoadBalancingInfo(
        available_cores=len(available_cores),
        total_cores=len(all_cores),
        total_current_requests=total_current_requests,
        total_capacity=total_capacity,
        utilization_rate=utilization_rate,
        cores_status=cores_status
    )
    
    return ResultResponse[LoadBalancingInfo](result=load_info)


@extraction_core_router.get(
    "/{core_id}/metrics",
    name="Get Core Performance Metrics",
    summary="获取核心性能指标",
    response_model=ResultResponse[CorePerformanceMetrics]
)
async def get_core_performance_metrics(
    core_id: int,
    days: int = 7,
    current_user: AdminUser = Depends(get_current_active_user)
) -> ResultResponse[CorePerformanceMetrics]:
    """获取提取核心性能指标"""
    
    core = await ExtractionCore.get_or_none(id=core_id)
    if not core or core.is_deleted:
        raise UserError('提取核心不存在')
    
    # 计算时间范围
    period_end = datetime.now()
    period_start = period_end - timedelta(days=days)
    
    # 这里应该从提取记录中统计相关数据
    # 由于简化，我们使用核心自身的统计数据
    
    # 计算吞吐量（简化计算）
    time_diff = (period_end - period_start).total_seconds()
    throughput = core.total_requests / time_diff if time_diff > 0 else 0
    
    # 计算错误率
    error_rate = 0.0
    if core.total_requests > 0:
        error_rate = (core.failed_requests / core.total_requests) * 100
    
    # 计算可用性（简化计算）
    availability = 100.0 if core.is_healthy else 0.0
    
    metrics = CorePerformanceMetrics(
        core_id=core_id,
        period_start=period_start,
        period_end=period_end,
        total_requests=core.total_requests,
        success_requests=core.success_requests,
        failed_requests=core.failed_requests,
        average_response_time=core.average_response_time,
        min_response_time=core.average_response_time * 0.5,  # 简化
        max_response_time=core.average_response_time * 2.0,  # 简化
        throughput=throughput,
        error_rate=error_rate,
        availability=availability
    )
    
    return ResultResponse[CorePerformanceMetrics](result=metrics)
