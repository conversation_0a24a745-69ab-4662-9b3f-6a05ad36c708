import axios from 'axios'

// Create axios instance
export const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:11211/api/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    // Handle common errors
    if (error.response?.status === 401) {
      // Unauthorized - clear token and redirect to login
      localStorage.removeItem('auth_token')
      window.location.href = '/login'
    }
    
    return Promise.reject(error)
  }
)

// API service functions
export const authAPI = {
  login: (username: string, password: string) =>
    api.post('/login', { username, password }),
  
  testToken: () =>
    api.get('/login/-test-token'),
  
  validateAccessToken: (token: string) =>
    api.post('/access-token/validate', { token })
}

export const tokenAPI = {
  list: (params?: any) =>
    api.get('/access-token', { params }),
  
  create: (data: any) =>
    api.post('/access-token', data),
  
  get: (id: number) =>
    api.get(`/access-token/${id}`),
  
  update: (id: number, data: any) =>
    api.put(`/access-token/${id}`, data),
  
  delete: (id: number) =>
    api.delete(`/access-token/${id}`),
  
  generateLink: (token: string, baseUrl?: string) =>
    api.post('/access-token/generate-link', { token, base_url: baseUrl }),
  
  getStats: (token: string) =>
    api.get(`/access-token/stats/${token}`)
}

export const extractionAPI = {
  extract: (data: { token: string; url: string; force_refresh?: boolean }) =>
    api.post('/extraction/extract', data),
  
  batchExtract: (data: { token: string; urls: string[]; force_refresh?: boolean }) =>
    api.post('/extraction/batch-extract', data),
  
  getHistory: (data: {
    token: string;
    page?: number;
    size?: number;
    status?: string;
    start_date?: string;
    end_date?: string;
  }) =>
    api.post('/extraction/history', data),
  
  getStats: (params?: any) =>
    api.get('/extraction/admin/stats', { params }),
  
  adminList: (params?: any) =>
    api.get('/extraction/admin/records', { params })
}

export const extractionCoreAPI = {
  list: (params?: any) =>
    api.get('/extraction-core', { params }),
  
  create: (data: any) =>
    api.post('/extraction-core', data),
  
  get: (id: number) =>
    api.get(`/extraction-core/${id}`),
  
  update: (id: number, data: any) =>
    api.put(`/extraction-core/${id}`, data),
  
  delete: (id: number) =>
    api.delete(`/extraction-core/${id}`),
  
  healthCheck: (id: number, data: any) =>
    api.post(`/extraction-core/${id}/health-check`, data),
  
  getStats: (id: number) =>
    api.get(`/extraction-core/${id}/stats`)
}

export const adminUserAPI = {
  list: (params?: any) =>
    api.get('/admin-user', { params }),
  
  create: (data: any) =>
    api.post('/admin-user', data),
  
  get: (id: number) =>
    api.get(`/admin-user/${id}`),
  
  update: (id: number, data: any) =>
    api.put(`/admin-user/${id}`, data),
  
  delete: (id: number) =>
    api.delete(`/admin-user/${id}`),
  
  changePassword: (data: { current_password: string; new_password: string }) =>
    api.post('/admin-user/change-password', data)
}

export default api
