from .base import BaseModel, fields
from enum import IntEnum
import hashlib
from datetime import datetime


class ExtractionStatus(IntEnum):
    """提取状态"""
    PENDING = 1     # 等待处理
    PROCESSING = 2  # 处理中
    SUCCESS = 3     # 成功
    FAILED = 4      # 失败
    DUPLICATE = 5   # 重复（已存在）


class ExtractionRecord(BaseModel):
    """文章提取记录表"""
    # 基础信息
    url = fields.CharField(max_length=1024, null=False, description="CSDN文章URL")
    url_hash = fields.CharField(max_length=64, unique=True, null=False, description="URL的MD5哈希")
    title = fields.CharField(max_length=256, null=True, description="文章标题")
    author = fields.CharField(max_length=128, null=True, description="文章作者")
    
    # 提取状态
    status: ExtractionStatus = fields.IntEnumField(ExtractionStatus, default=ExtractionStatus.PENDING, description="提取状态")
    
    # 提取结果
    content = fields.TextField(null=True, description="提取的文章内容（图片链接已替换为代理地址）")
    original_content = fields.TextField(null=True, description="原始文章内容（未处理的MD内容）")
    content_length = fields.IntField(null=True, description="内容长度")
    
    # 关联信息
    access_token = fields.ForeignKeyField('template.AccessToken', related_name='extraction_records', description="使用的访问令牌")
    
    # 提取时间信息
    request_time = fields.DatetimeField(auto_now_add=True, description="请求时间")
    complete_time = fields.DatetimeField(null=True, description="完成时间")
    
    # 提取来源信息
    client_ip = fields.CharField(max_length=45, null=True, description="客户端IP")
    user_agent = fields.CharField(max_length=512, null=True, description="用户代理")
    
    # 错误信息
    error_message = fields.TextField(null=True, description="错误信息")
    retry_count = fields.IntField(default=0, description="重试次数")
    
    # 提取核心信息
    core_response_time = fields.FloatField(null=True, description="核心API响应时间（秒）")
    core_server = fields.CharField(max_length=128, null=True, description="处理的核心服务器")

    class Meta:
        table = "extraction_record"
        indexes = [
            ("url_hash",),
            ("access_token", "create_time"),
            ("status", "create_time"),
        ]
        
    class PydanticMeta:
        exclude = ['is_deleted']

    @classmethod
    def generate_url_hash(cls, url: str) -> str:
        """生成URL的MD5哈希"""
        return hashlib.md5(url.encode('utf-8')).hexdigest()

    @classmethod
    async def check_duplicate(cls, url: str) -> bool:
        """检查URL是否已经提取过"""
        url_hash = cls.generate_url_hash(url)
        existing = await cls.filter(url_hash=url_hash, status=ExtractionStatus.SUCCESS).first()
        return existing is not None

    @classmethod
    async def get_by_url(cls, url: str):
        """根据URL获取记录"""
        url_hash = cls.generate_url_hash(url)
        return await cls.filter(url_hash=url_hash).first()

    async def mark_success(self, title: str = None, author: str = None, content: str = None, original_content: str = None):
        """标记为成功"""
        self.status = ExtractionStatus.SUCCESS
        self.complete_time = datetime.now()
        if title:
            self.title = title
        if author:
            self.author = author
        if content:
            self.content = content
            self.content_length = len(content)
        if original_content:
            self.original_content = original_content
        await self.save()

    async def mark_failed(self, error_message: str = None):
        """标记为失败"""
        self.status = ExtractionStatus.FAILED
        self.complete_time = datetime.now()
        if error_message:
            self.error_message = error_message
        await self.save()

    async def mark_duplicate(self, original_record):
        """标记为重复"""
        self.status = ExtractionStatus.DUPLICATE
        self.complete_time = datetime.now()
        # 复制原记录的内容信息
        if original_record:
            self.title = original_record.title
            self.author = original_record.author
            self.content = original_record.content
            self.content_length = original_record.content_length
        await self.save()
