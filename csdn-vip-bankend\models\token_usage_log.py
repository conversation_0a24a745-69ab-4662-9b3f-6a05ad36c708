from .base import BaseModel, fields
from enum import IntEnum
from datetime import datetime, timedelta
from utils.datetime.tztime import get_sh_tz_time_aware


class UsageType(IntEnum):
    """使用类型"""
    EXTRACTION = 1   # 文章提取
    AUTH_CHECK = 2   # 权限检查
    OTHER = 3        # 其他


class UsageStatus(IntEnum):
    """使用状态"""
    SUCCESS = 1      # 成功
    FAILED = 2       # 失败
    BLOCKED = 3      # 被阻止（超限等）


class TokenUsageLog(BaseModel):
    """令牌使用日志表"""
    # 关联信息
    access_token = fields.ForeignKeyField('template.AccessToken', related_name='usage_logs', description="访问令牌")
    extraction_record = fields.ForeignKeyField('template.ExtractionRecord', related_name='usage_logs', null=True, description="提取记录")
    
    # 使用信息
    usage_type: UsageType = fields.IntEnumField(UsageType, description="使用类型")
    status: UsageStatus = fields.IntEnumField(UsageStatus, description="使用状态")
    
    # 请求信息
    client_ip = fields.CharField(max_length=45, null=True, description="客户端IP")
    user_agent = fields.CharField(max_length=512, null=True, description="用户代理")
    request_url = fields.CharField(max_length=1024, null=True, description="请求URL")
    
    # 时间信息
    request_time = fields.DatetimeField(auto_now_add=True, description="请求时间")
    response_time = fields.FloatField(null=True, description="响应时间（秒）")
    
    # 错误信息
    error_code = fields.CharField(max_length=32, null=True, description="错误代码")
    error_message = fields.TextField(null=True, description="错误信息")
    
    # 额外信息
    extra_data = fields.JSONField(null=True, description="额外数据（JSON格式）")

    class Meta:
        table = "token_usage_log"
        indexes = [
            ("access_token", "request_time"),
            ("client_ip", "request_time"),
            ("usage_type", "status", "request_time"),
        ]
        
    class PydanticMeta:
        exclude = ['is_deleted']

    @classmethod
    async def check_rate_limit(cls, token_id: int, client_ip: str = None, time_window_hours: int = 1, max_requests: int = 10) -> bool:
        """检查速率限制"""
        start_time = get_sh_tz_time_aware() - timedelta(hours=time_window_hours)
        
        # 构建查询条件
        filters = {
            'access_token_id': token_id,
            'request_time__gte': start_time,
            'status': UsageStatus.SUCCESS
        }
        
        if client_ip:
            filters['client_ip'] = client_ip
        
        # 统计时间窗口内的请求数
        count = await cls.filter(**filters).count()
        
        # 如果没有设置限制，则不限制
        if max_requests is None:
            return True
        return count < max_requests

    @classmethod
    async def check_daily_limit(cls, token_id: int, max_daily_requests: int = None) -> bool:
        """检查每日限制"""
        start_time = get_sh_tz_time_aware().replace(hour=0, minute=0, second=0, microsecond=0)
        
        count = await cls.filter(
            access_token_id=token_id,
            request_time__gte=start_time,
            status=UsageStatus.SUCCESS
        ).count()
        
        # 如果没有设置限制，则不限制
        if max_daily_requests is None:
            return True
        return count < max_daily_requests

    @classmethod
    async def get_usage_stats(cls, token_id: int, days: int = 7) -> dict:
        """获取使用统计"""
        start_time = get_sh_tz_time_aware() - timedelta(days=days)
        
        logs = await cls.filter(
            access_token_id=token_id,
            request_time__gte=start_time
        ).all()
        
        stats = {
            'total_requests': len(logs),
            'success_requests': len([log for log in logs if log.status == UsageStatus.SUCCESS]),
            'failed_requests': len([log for log in logs if log.status == UsageStatus.FAILED]),
            'blocked_requests': len([log for log in logs if log.status == UsageStatus.BLOCKED]),
            'unique_ips': len(set(log.client_ip for log in logs if log.client_ip)),
            'average_response_time': 0.0
        }
        
        # 计算平均响应时间
        response_times = [log.response_time for log in logs if log.response_time is not None]
        if response_times:
            stats['average_response_time'] = sum(response_times) / len(response_times)
        
        # 按天分组统计
        daily_stats = {}
        for log in logs:
            date_key = log.request_time.strftime('%Y-%m-%d')
            if date_key not in daily_stats:
                daily_stats[date_key] = {'total': 0, 'success': 0, 'failed': 0, 'blocked': 0}
            
            daily_stats[date_key]['total'] += 1
            if log.status == UsageStatus.SUCCESS:
                daily_stats[date_key]['success'] += 1
            elif log.status == UsageStatus.FAILED:
                daily_stats[date_key]['failed'] += 1
            elif log.status == UsageStatus.BLOCKED:
                daily_stats[date_key]['blocked'] += 1
        
        stats['daily_stats'] = daily_stats
        return stats

    @classmethod
    async def log_usage(cls, token_id: int, usage_type: UsageType, status: UsageStatus, 
                       client_ip: str = None, user_agent: str = None, request_url: str = None,
                       response_time: float = None, error_code: str = None, error_message: str = None,
                       extraction_record_id: int = None, extra_data: dict = None):
        """记录使用日志"""
        return await cls.create(
            access_token_id=token_id,
            extraction_record_id=extraction_record_id,
            usage_type=usage_type,
            status=status,
            client_ip=client_ip,
            user_agent=user_agent,
            request_url=request_url,
            response_time=response_time,
            error_code=error_code,
            error_message=error_message,
            extra_data=extra_data
        )
