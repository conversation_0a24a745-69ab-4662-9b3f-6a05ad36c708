<template>
  <div class="space-y-6">
    <!-- Page header -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">仪表板</h1>
      <p class="text-gray-600">系统概览和统计信息</p>
    </div>

    <!-- Stats cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div class="card">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <KeyIcon class="h-8 w-8 text-primary-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">活跃令牌</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.activeTokens }}</p>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <DocumentTextIcon class="h-8 w-8 text-green-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">今日提取</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.todayExtractions }}</p>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <ChartBarIcon class="h-8 w-8 text-blue-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">成功率</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.successRate }}%</p>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <ServerIcon class="h-8 w-8 text-purple-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">提取核心</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.activeCores }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts and recent activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Recent extractions -->
      <div class="card">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-lg font-semibold text-gray-900">最近提取</h2>
          <router-link
            to="/admin/history"
            class="text-sm text-primary-600 hover:text-primary-700"
          >
            查看全部
          </router-link>
        </div>

        <div v-if="recentExtractions.length === 0" class="text-center text-gray-500 py-8">
          暂无提取记录
        </div>

        <div v-else class="space-y-3">
          <div
            v-for="extraction in recentExtractions"
            :key="extraction.id"
            class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
          >
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 truncate">
                {{ extraction.title || '未知标题' }}
              </p>
              <p class="text-xs text-gray-500 truncate">
                {{ extraction.url }}
              </p>
            </div>
            <div class="flex-shrink-0 ml-4">
              <span
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                :class="{
                  'bg-green-100 text-green-800': extraction.status === 'SUCCESS',
                  'bg-red-100 text-red-800': extraction.status === 'FAILED',
                  'bg-blue-100 text-blue-800': extraction.status === 'DUPLICATE'
                }"
              >
                {{ getStatusText(extraction.status) }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- System status -->
      <div class="card">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">系统状态</h2>

        <div class="space-y-4">
          <!-- API status -->
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
              <span class="text-sm text-gray-700">API 服务</span>
            </div>
            <span class="text-sm text-green-600 font-medium">正常</span>
          </div>

          <!-- Database status -->
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
              <span class="text-sm text-gray-700">数据库</span>
            </div>
            <span class="text-sm text-green-600 font-medium">正常</span>
          </div>

          <!-- Extraction cores -->
          <div class="space-y-2">
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-700">提取核心</span>
              <span class="text-sm text-gray-600">{{ extractionCores.length }} 个</span>
            </div>
            <div
              v-for="core in extractionCores"
              :key="core.id"
              class="flex items-center justify-between pl-4"
            >
              <div class="flex items-center">
                <div
                  class="w-2 h-2 rounded-full mr-2"
                  :class="{
                    'bg-green-500': core.status === 'ACTIVE',
                    'bg-red-500': core.status === 'INACTIVE',
                    'bg-yellow-500': core.status === 'ERROR'
                  }"
                ></div>
                <span class="text-xs text-gray-600">{{ core.name }}</span>
              </div>
              <span
                class="text-xs"
                :class="{
                  'text-green-600': core.status === 'ACTIVE',
                  'text-red-600': core.status === 'INACTIVE',
                  'text-yellow-600': core.status === 'ERROR'
                }"
              >
                {{ getCoreStatusText(core.status) }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick actions -->
    <div class="card">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">快捷操作</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <router-link
          to="/admin/tokens"
          class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
        >
          <KeyIcon class="h-6 w-6 text-primary-600 mr-3" />
          <div>
            <p class="font-medium text-gray-900">管理令牌</p>
            <p class="text-sm text-gray-600">创建和管理访问令牌</p>
          </div>
        </router-link>

        <router-link
          to="/admin/extraction-cores"
          class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
        >
          <ServerIcon class="h-6 w-6 text-purple-600 mr-3" />
          <div>
            <p class="font-medium text-gray-900">提取核心</p>
            <p class="text-sm text-gray-600">管理提取核心服务</p>
          </div>
        </router-link>

        <router-link
          to="/admin/users"
          class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
        >
          <UsersIcon class="h-6 w-6 text-green-600 mr-3" />
          <div>
            <p class="font-medium text-gray-900">用户管理</p>
            <p class="text-sm text-gray-600">管理系统用户</p>
          </div>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { tokenAPI, extractionAPI, extractionCoreAPI } from '@/services/api'
import {
  KeyIcon,
  DocumentTextIcon,
  ChartBarIcon,
  ServerIcon,
  UsersIcon
} from '@heroicons/vue/24/outline'
import type { ExtractionRecord, ExtractionCore, ExtractionStatus, CoreStatus } from '@/types'

const stats = reactive({
  activeTokens: 0,
  todayExtractions: 0,
  successRate: 0,
  activeCores: 0
})

const recentExtractions = ref<ExtractionRecord[]>([])
const extractionCores = ref<ExtractionCore[]>([])

const loadDashboardData = async () => {
  try {
    // Load stats
    const [tokensRes, extractionsRes, coresRes] = await Promise.all([
      tokenAPI.list({ status: 'ACTIVE' }),
      extractionAPI.getStats({ days: 7 }),
      extractionCoreAPI.list()
    ])

    // 适配后端响应格式 {code: 200, result: {...}}
    if (tokensRes.data.code === 200) {
      stats.activeTokens = tokensRes.data.result.total || 0
    }

    if (extractionsRes.data.code === 200) {
      const extractionStats = extractionsRes.data.result
      stats.todayExtractions = extractionStats.today_extractions || 0
      stats.successRate = Math.round(extractionStats.success_rate || 0)
    }

    if (coresRes.data.code === 200) {
      extractionCores.value = coresRes.data.result.items || []
      stats.activeCores = extractionCores.value.filter(core => core.status === 'ACTIVE').length
    }

    // Load recent extractions
    const recentRes = await extractionAPI.adminList({ page: 1, page_size: 5 })
    if (recentRes.data.code === 200) {
      // 后端返回的是直接的数组，不是分页格式
      recentExtractions.value = (recentRes.data.result || []).slice(0, 5)
    }
  } catch (error) {
    console.error('Failed to load dashboard data:', error)
  }
}

const getStatusText = (status: ExtractionStatus) => {
  const statusMap = {
    SUCCESS: '成功',
    FAILED: '失败',
    DUPLICATE: '重复',
    PENDING: '处理中'
  }
  return statusMap[status] || status
}

const getCoreStatusText = (status: CoreStatus) => {
  const statusMap = {
    ACTIVE: '正常',
    INACTIVE: '离线',
    ERROR: '错误'
  }
  return statusMap[status] || status
}

onMounted(() => {
  loadDashboardData()
})
</script>
