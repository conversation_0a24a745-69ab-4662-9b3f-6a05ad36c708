#!/usr/bin/env python3
"""
系统初始化脚本
用于创建初始管理员用户、提取核心配置等
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from tortoise import Tortoise
from config import settings
from models import AdminUser, ExtractionCore, AccessToken, CoreStatus
from core.auth.base import get_password_hash
from datetime import datetime, timedelta
import secrets
import string
from utils.datetime.tztime import get_sh_tz_time_aware


async def init_database():
    """初始化数据库连接"""
    # 确保数据目录存在
    import os
    db_path = settings.SQLITE.DATABASE
    db_dir = os.path.dirname(db_path)
    if db_dir and not os.path.exists(db_dir):
        os.makedirs(db_dir, exist_ok=True)
        print(f"✓ 创建数据库目录: {db_dir}")
    
    TORTOISE_ORM = {
        "connections": {
            "sqlite": f"sqlite://{settings.SQLITE.DATABASE}"
        },
        "apps": {
            "template": {
                "models": [
                    "models",
                    "aerich.models",
                    "casbin_tortoise_adapter",
                ],
                "default_connection": "sqlite",
            },
        },
        'use_tz': False,
        'timezone': 'Asia/Shanghai'
    }
    
    await Tortoise.init(TORTOISE_ORM)
    await Tortoise.generate_schemas()
    print(f"✓ SQLite数据库初始化完成: {db_path}")


async def create_admin_user():
    """创建初始管理员用户"""
    # 检查是否已存在管理员
    existing_admin = await AdminUser.filter(username="admin").first()
    if existing_admin:
        print("✓ 管理员用户已存在，跳过创建")
        return existing_admin
    
    # 创建管理员用户
    admin_password = "admin123456"  # 生产环境应该使用更强的密码
    admin_user = await AdminUser.create(
        username="admin",
        nickname="系统管理员",
        hashed_password=get_password_hash(admin_password),
        email="<EMAIL>",
        is_super=True,
        is_active=True,
        last_login_time=get_sh_tz_time_aware()
    )
    
    print(f"✓ 创建管理员用户成功")
    print(f"  用户名: {admin_user.username}")
    print(f"  密码: {admin_password}")
    print(f"  邮箱: {admin_user.email}")
    print("  ⚠️  请及时修改默认密码！")
    
    return admin_user


async def create_extraction_core():
    """创建示例提取核心配置"""
    # 检查是否已存在提取核心
    existing_core = await ExtractionCore.filter(name="默认提取核心").first()
    if existing_core:
        print("✓ 提取核心配置已存在，跳过创建")
        return existing_core
    
    # 创建示例提取核心
    core = await ExtractionCore.create(
        name="默认提取核心",
        description="默认的CSDN文章提取核心服务",
        api_endpoint="http://localhost:8080/api/extract",  # 需要替换为实际的核心API
        auth_token="demo_core_token_" + ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(16)),
        status=CoreStatus.INACTIVE,  # 默认设为非激活状态，需要管理员配置
        timeout_seconds=30,
        max_retries=3,
        priority=1,
        weight=1,
        max_concurrent_requests=10,
        health_check_url="http://localhost:8080/api/health",
        is_healthy=False
    )
    
    print(f"✓ 创建提取核心配置成功")
    print(f"  名称: {core.name}")
    print(f"  端点: {core.api_endpoint}")
    print(f"  状态: 非激活（需要管理员配置实际的API端点）")
    
    return core


async def create_demo_access_token(admin_user):
    """创建演示访问令牌"""
    # 检查是否已存在演示令牌
    existing_token = await AccessToken.filter(name="演示令牌").first()
    if existing_token:
        print("✓ 演示访问令牌已存在，跳过创建")
        return existing_token
    
    # 生成令牌
    token_string = 'demo_' + ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(32))
    
    # 创建演示令牌（7天有效期，1000次使用限制）
    demo_token = await AccessToken.create(
        token=token_string,
        name="演示令牌",
        max_usage_count=1000,
        expire_time=get_sh_tz_time_aware() + timedelta(days=7),
        max_daily_usage=100,
        max_hourly_usage=10,
        notes="系统初始化创建的演示令牌，用于测试",
        created_by=admin_user
    )
    
    print(f"✓ 创建演示访问令牌成功")
    print(f"  令牌: {demo_token.token}")
    print(f"  类型: 时间限制型")
    print(f"  有效期: 7天")
    print(f"  最大使用次数: 1000次")
    print(f"  每日限额: 100次")
    print(f"  每小时限额: 10次")
    
    return demo_token


async def show_system_info():
    """显示系统信息"""
    print("\n" + "="*60)
    print("🎉 CSDN文章提取系统初始化完成！")
    print("="*60)
    
    # 统计信息
    admin_count = await AdminUser.filter(is_deleted=False).count()
    core_count = await ExtractionCore.filter(is_deleted=False).count()
    token_count = await AccessToken.filter(is_deleted=False).count()
    
    print(f"📊 系统统计:")
    print(f"  - 管理员用户: {admin_count}")
    print(f"  - 提取核心: {core_count}")
    print(f"  - 访问令牌: {token_count}")
    
    print(f"\n🚀 下一步操作:")
    print(f"  1. 启动后端服务: python main.py")
    print(f"  2. 启动前端服务: cd csdn-vip-frontend && npm install && npm run dev")
    print(f"  3. 访问管理后台: http://localhost:5173/admin")
    print(f"  4. 配置提取核心的实际API端点")
    print(f"  5. 修改默认管理员密码")
    
    print(f"\n⚠️  重要提醒:")
    print(f"  - 请务必修改默认管理员密码")
    print(f"  - 配置实际的提取核心API端点")
    print(f"  - 生产环境部署时请更新安全配置")
    
    print("\n" + "="*60)


async def main():
    """主函数"""
    print("🚀 开始初始化CSDN文章提取系统...")
    
    try:
        # 初始化数据库
        await init_database()
        
        # 创建管理员用户
        admin_user = await create_admin_user()
        
        # 创建提取核心配置
        await create_extraction_core()
        
        # 创建演示访问令牌
        await create_demo_access_token(admin_user)
        
        # 显示系统信息
        await show_system_info()
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(main())
