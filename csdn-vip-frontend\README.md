# CSDN VIP 前端系统

基于 Vue 3 + TypeScript + Vite 构建的 CSDN 文章提取系统前端界面。

## 功能特性

### 用户功能
- **智能门户**: 自动检测令牌，无令牌时显示输入界面
- **文章提取**: 单篇和批量提取 CSDN 文章
- **提取历史**: 查看个人提取记录，支持筛选和分页
- **实时状态**: 显示令牌使用情况和剩余次数
- **内容查看**: 模态框查看提取的文章内容

### 管理员功能
- **仪表板**: 系统概览和统计信息
- **令牌管理**: 创建、编辑、删除访问令牌
- **用户管理**: 管理系统管理员账户
- **提取核心**: 管理文章提取服务
- **历史记录**: 查看所有提取记录
- **个人资料**: 修改个人信息和密码

## 技术栈

- **框架**: Vue 3 (Composition API)
- **语言**: TypeScript
- **构建工具**: Vite
- **路由**: Vue Router 4
- **状态管理**: Pinia
- **HTTP 客户端**: Axios
- **UI 组件**: Headless UI + Heroicons
- **样式**: Tailwind CSS
- **工具库**: VueUse

## 开发指南

### 环境要求

- Node.js >= 16
- npm >= 8

### 安装依赖

```bash
npm install
```

### 开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 环境配置

创建 `.env` 文件配置环境变量：

```env
VITE_API_BASE_URL=http://localhost:11211/api/v1
VITE_APP_TITLE=CSDN VIP 管理系统
```

## 路由说明

### 用户路由
- `/` - 用户门户（整合单篇提取、批量提取和历史功能）

### 管理员路由
- `/login` - 登录页面
- `/admin` - 管理员仪表板（需要认证）
- `/admin/tokens` - 令牌管理（需要认证）
- `/admin/users` - 用户管理（需要认证）
- `/admin/extraction-cores` - 提取核心管理（需要认证）
- `/admin/history` - 提取历史管理（需要认证）
- `/admin/profile` - 个人资料（需要认证）

## 状态管理

使用 Pinia 进行状态管理：

- **auth**: 认证状态、用户信息、令牌管理

## API 集成

所有 API 调用通过 `src/services/api.ts` 统一管理，包括：

- 认证相关 API
- 令牌管理 API
- 文章提取 API
- 用户管理 API
- 提取核心管理 API

## 错误处理

- 全局错误处理通过 `useErrorHandler` 组合式函数
- 统一的通知系统通过 `useNotifications` 组合式函数
- 加载状态管理通过 `useLoading` 组合式函数

## 样式规范

- 使用 Tailwind CSS 进行样式开发
- 响应式设计，支持移动端
- 统一的组件样式类：
  - `.btn` - 按钮基础样式
  - `.btn-primary` - 主要按钮
  - `.btn-secondary` - 次要按钮
  - `.btn-danger` - 危险按钮
  - `.input` - 输入框样式
  - `.card` - 卡片容器样式

## 部署说明

1. 构建生产版本：`npm run build`
2. 将 `dist` 目录部署到 Web 服务器
3. 配置 Web 服务器支持 SPA 路由（所有路由指向 `index.html`）

## 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88
