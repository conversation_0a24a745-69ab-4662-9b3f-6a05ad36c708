<template>
  <div class="space-y-6">
    <!-- Page header -->
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">访问令牌管理</h1>
        <p class="text-gray-600">创建和管理用户访问令牌</p>
      </div>
      <button
        @click="showCreateModal = true"
        class="btn btn-primary"
      >
        创建令牌
      </button>
    </div>

    <!-- Filters -->
    <div class="card">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
          <select v-model="filters.status" @change="loadTokens" class="input">
            <option value="">全部状态</option>
            <option value="ACTIVE">活跃</option>
            <option value="EXPIRED">已过期</option>
            <option value="DISABLED">已禁用</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">搜索</label>
          <input
            v-model="filters.search"
            @input="debounceSearch"
            type="text"
            placeholder="搜索令牌名称..."
            class="input"
          />
        </div>

        <div class="flex items-end">
          <button
            @click="loadTokens"
            :disabled="loading"
            class="btn btn-secondary w-full"
          >
            {{ loading ? '查询中...' : '刷新' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Tokens table -->
    <div class="card">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                令牌信息
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                限制条件
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                使用情况
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                创建时间
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-if="loading">
              <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                <div class="flex items-center justify-center">
                  <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-primary-600 mr-2"></div>
                  加载中...
                </div>
              </td>
            </tr>

            <tr v-else-if="tokens.length === 0">
              <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                暂无令牌记录
              </td>
            </tr>
            
            <tr v-else v-for="token in tokens" :key="token.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div>
                  <div class="text-sm font-medium text-gray-900">{{ token.name }}</div>
                  <div class="text-sm text-gray-500 font-mono">{{ token.token.substring(0, 16) }}...</div>
                  <div v-if="token.notes" class="text-xs text-gray-400 mt-1">{{ token.notes }}</div>
                </div>
              </td>
              
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="{
                    'bg-green-100 text-green-800': token.status === 'ACTIVE',
                    'bg-red-100 text-red-800': token.status === 'EXPIRED',
                    'bg-gray-100 text-gray-800': token.status === 'DISABLED'
                  }"
                >
                  {{ getStatusText(token.status) }}
                </span>
              </td>

              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div class="space-y-1">
                  <div v-if="token.max_usage_count" class="text-xs">
                    次数限制: {{ token.max_usage_count }}
                  </div>
                  <div v-if="token.expire_time" class="text-xs">
                    时间限制: {{ formatDate(token.expire_time) }}
                  </div>
                  <div v-if="token.max_daily_usage" class="text-xs">
                    日限制: {{ token.max_daily_usage }}
                  </div>
                  <div v-if="token.max_hourly_usage" class="text-xs">
                    时限制: {{ token.max_hourly_usage }}
                  </div>
                  <div v-if="token.client_ip" class="text-xs">
                    IP限制: {{ token.client_ip }}
                  </div>
                  <div v-if="!token.max_usage_count && !token.expire_time && !token.max_daily_usage && !token.max_hourly_usage && !token.client_ip" class="text-xs text-gray-500">
                    无限制
                  </div>
                </div>
              </td>

              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div>
                  <div>已使用: {{ token.current_usage_count }}</div>
                  <div v-if="token.max_usage_count" class="text-xs text-gray-500">
                    剩余: {{ token.max_usage_count - token.current_usage_count }}
                  </div>
                </div>
              </td>
              
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <div>
                  <div>{{ formatDate(token.create_time) }}</div>
                  <div class="text-xs">{{ token.created_by.username }}</div>
                </div>
              </td>
              
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                <button
                  @click="generateLink(token)"
                  class="text-primary-600 hover:text-primary-900"
                >
                  生成链接
                </button>
                <button
                  @click="editToken(token)"
                  class="text-indigo-600 hover:text-indigo-900"
                >
                  编辑
                </button>
                <button
                  @click="deleteToken(token)"
                  class="text-red-600 hover:text-red-900"
                >
                  删除
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <!-- Pagination -->
      <div v-if="pagination.pages > 1" class="px-6 py-4 border-t border-gray-200">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-700">
            显示第 {{ (pagination.page - 1) * pagination.size + 1 }} - 
            {{ Math.min(pagination.page * pagination.size, pagination.total) }} 条，
            共 {{ pagination.total }} 条记录
          </div>
          
          <div class="flex space-x-2">
            <button
              @click="changePage(pagination.page - 1)"
              :disabled="pagination.page <= 1"
              class="btn btn-secondary text-sm"
            >
              上一页
            </button>
            
            <button
              @click="changePage(pagination.page + 1)"
              :disabled="pagination.page >= pagination.pages"
              class="btn btn-secondary text-sm"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Create/Edit Modal -->
    <div
      v-if="showCreateModal || editingToken"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
    >
      <div class="bg-white rounded-lg max-w-md w-full p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">
          {{ editingToken ? '编辑令牌' : '创建令牌' }}
        </h3>
        
        <form @submit.prevent="saveToken" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">名称</label>
            <input
              v-model="tokenForm.name"
              type="text"
              required
              class="input"
              placeholder="令牌名称"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">最大使用次数</label>
            <input
              v-model.number="tokenForm.max_usage_count"
              type="number"
              min="1"
              class="input"
              placeholder="留空表示无限制"
            />
            <p class="text-xs text-gray-500 mt-1">不设置则无次数限制</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">过期时间</label>
            <input
              v-model="tokenForm.expire_time"
              type="datetime-local"
              class="input"
            />
            <p class="text-xs text-gray-500 mt-1">不设置则永不过期</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">每日最大使用次数</label>
            <input
              v-model.number="tokenForm.max_daily_usage"
              type="number"
              min="1"
              class="input"
              placeholder="留空表示无限制"
            />
            <p class="text-xs text-gray-500 mt-1">每日使用次数限制</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">每小时最大使用次数</label>
            <input
              v-model.number="tokenForm.max_hourly_usage"
              type="number"
              min="1"
              class="input"
              placeholder="留空表示无限制"
            />
            <p class="text-xs text-gray-500 mt-1">每小时使用次数限制</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">客户端IP限制</label>
            <input
              v-model="tokenForm.client_ip"
              type="text"
              class="input"
              placeholder="留空表示不限制IP"
            />
            <p class="text-xs text-gray-500 mt-1">限制只能从指定IP访问</p>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">备注</label>
            <textarea
              v-model="tokenForm.notes"
              rows="3"
              class="input"
              placeholder="可选备注信息"
            ></textarea>
          </div>
          
          <div class="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              @click="closeModal"
              class="btn btn-secondary"
            >
              取消
            </button>
            <button
              type="submit"
              :disabled="saving"
              class="btn btn-primary"
            >
              {{ saving ? '保存中...' : '保存' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Link Modal -->
    <div
      v-if="generatedLink"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
    >
      <div class="bg-white rounded-lg max-w-lg w-full p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">访问链接</h3>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">生成的访问链接</label>
            <div class="flex">
              <input
                :value="generatedLink"
                readonly
                class="input flex-1 mr-2 font-mono text-sm"
              />
              <button
                @click="copyLink"
                class="btn btn-secondary"
              >
                复制
              </button>
            </div>
          </div>
          
          <div class="bg-blue-50 p-4 rounded-lg">
            <p class="text-sm text-blue-800">
              将此链接发送给用户，用户可以通过此链接访问文章提取功能。
            </p>
          </div>
        </div>
        
        <div class="flex justify-end pt-4">
          <button
            @click="generatedLink = null"
            class="btn btn-primary"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { tokenAPI } from '@/services/api'
import type { AccessToken, AccessTokenCreate, TokenStatus } from '@/types'

const loading = ref(false)
const saving = ref(false)
const tokens = ref<AccessToken[]>([])
const showCreateModal = ref(false)
const editingToken = ref<AccessToken | null>(null)
const generatedLink = ref<string | null>(null)

const filters = reactive({
  status: '' as TokenStatus | '',
  search: ''
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0,
  pages: 0
})

const tokenForm = reactive<AccessTokenCreate>({
  name: '',
  max_usage_count: undefined,
  expire_time: undefined,
  max_daily_usage: undefined,
  max_hourly_usage: undefined,
  client_ip: '',
  notes: ''
})

let searchTimeout: NodeJS.Timeout

const loadTokens = async () => {
  loading.value = true
  
  try {
    const response = await tokenAPI.list({
      page: pagination.page,
      size: pagination.size,
      status: filters.status || undefined,
      search: filters.search || undefined
    })
    
    const result = response.data.result
    tokens.value = result.items
    pagination.total = result.total
    pagination.pages = result.pages
  } catch (error) {
    console.error('Failed to load tokens:', error)
  } finally {
    loading.value = false
  }
}

const debounceSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    pagination.page = 1
    loadTokens()
  }, 500)
}

const changePage = (page: number) => {
  pagination.page = page
  loadTokens()
}

const editToken = (token: AccessToken) => {
  editingToken.value = token
  Object.assign(tokenForm, {
    name: token.name,
    max_usage_count: token.max_usage_count,
    expire_time: token.expire_time ? token.expire_time.slice(0, 16) : undefined,
    max_daily_usage: token.max_daily_usage,
    max_hourly_usage: token.max_hourly_usage,
    client_ip: token.client_ip || '',
    notes: token.notes || ''
  })
}

const closeModal = () => {
  showCreateModal.value = false
  editingToken.value = null
  Object.assign(tokenForm, {
    name: '',
    max_usage_count: undefined,
    expire_time: undefined,
    max_daily_usage: undefined,
    max_hourly_usage: undefined,
    client_ip: '',
    notes: ''
  })
}

const saveToken = async () => {
  saving.value = true
  
  try {
    const data = { ...tokenForm }
    if (data.expire_time) {
      data.expire_time = new Date(data.expire_time).toISOString()
    }
    
    if (editingToken.value) {
      await tokenAPI.update(editingToken.value.id, data)
    } else {
      await tokenAPI.create(data)
    }
    
    closeModal()
    await loadTokens()
  } catch (error) {
    console.error('Failed to save token:', error)
  } finally {
    saving.value = false
  }
}

const deleteToken = async (token: AccessToken) => {
  if (!confirm(`确定要删除令牌 "${token.name}" 吗？`)) return
  
  try {
    await tokenAPI.delete(token.id)
    await loadTokens()
  } catch (error) {
    console.error('Failed to delete token:', error)
  }
}

const generateLink = async (token: AccessToken) => {
  try {
    const response = await tokenAPI.generateLink(token.token)
    generatedLink.value = response.data.result.access_link
  } catch (error) {
    console.error('Failed to generate link:', error)
  }
}

const copyLink = async () => {
  if (generatedLink.value) {
    try {
      await navigator.clipboard.writeText(generatedLink.value)
      // Could show a toast notification here
    } catch (error) {
      console.error('Failed to copy link:', error)
    }
  }
}

const getStatusText = (status: TokenStatus) => {
  const statusMap = {
    ACTIVE: '活跃',
    EXPIRED: '已过期',
    DISABLED: '已禁用'
  }
  return statusMap[status] || status
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  loadTokens()
})
</script>
