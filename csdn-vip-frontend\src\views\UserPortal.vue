<template>
  <div>
    <!-- Token Input Page -->
    <div v-if="!hasValidToken" class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4">
      <div class="max-w-md w-full">
        <!-- Logo and Title -->
        <div class="text-center mb-8">
          <div class="mx-auto h-16 w-16 bg-indigo-600 rounded-full flex items-center justify-center mb-4">
            <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>
          <h1 class="text-3xl font-bold text-gray-900">CSDN 文章提取工具</h1>
          <p class="text-gray-600 mt-2">输入您的访问令牌开始使用</p>
        </div>

        <!-- Token Input Form -->
        <div class="bg-white rounded-lg shadow-lg p-6">
          <form @submit.prevent="validateAndEnter" class="space-y-4">
            <div>
              <label for="token" class="block text-sm font-medium text-gray-700 mb-2">
                访问令牌
              </label>
              <input
                id="token"
                v-model="tokenInput"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="请输入您的访问令牌"
                :disabled="validating"
              />
            </div>
            
            <button
              type="submit"
              :disabled="validating || !tokenInput.trim()"
              class="w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="validating" class="flex items-center justify-center">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                验证中...
              </span>
              <span v-else>进入系统</span>
            </button>
          </form>
          
          <!-- Error message -->
          <div v-if="tokenError" class="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p class="text-sm text-red-600">{{ tokenError }}</p>
          </div>
        </div>

        <!-- System Info -->
        <div class="mt-8 bg-white rounded-lg shadow-lg p-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">系统说明</h2>
          <div class="space-y-3 text-sm text-gray-600">
            <div class="flex items-start">
              <div class="flex-shrink-0 w-2 h-2 bg-indigo-500 rounded-full mt-2 mr-3"></div>
              <p>支持单篇提取 CSDN 文章内容，包括标题、作者、发布时间和正文</p>
            </div>
            <div class="flex items-start">
              <div class="flex-shrink-0 w-2 h-2 bg-indigo-500 rounded-full mt-2 mr-3"></div>
              <p>提供完整的提取历史记录，支持查看和搜索</p>
            </div>
            <div class="flex items-start">
              <div class="flex-shrink-0 w-2 h-2 bg-indigo-500 rounded-full mt-2 mr-3"></div>
              <p>智能缓存机制，避免重复提取相同文章</p>
            </div>
            <div class="flex items-start">
              <div class="flex-shrink-0 w-2 h-2 bg-indigo-500 rounded-full mt-2 mr-3"></div>
              <p>支持Markdown格式渲染，便于阅读</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main User Interface -->
    <div v-else class="min-h-screen bg-gray-50">
      <!-- Header -->
      <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-full mx-auto px-6">
          <div class="flex justify-between items-center py-4">
            <h1 class="text-2xl font-bold text-gray-900">CSDN 文章提取工具</h1>
          </div>
        </div>
      </header>

      <!-- Main Content - 双栏布局 -->
      <div class="flex h-[calc(100vh-73px)]">
        <!-- 左栏 -->
        <div class="w-1/3 bg-white border-r border-gray-200 flex flex-col">
          <!-- 文章提取区域 -->
          <div class="p-6 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">文章提取区域</h2>
            
            <form @submit.prevent="handleSingleExtract" class="space-y-4">
              <div>
                <label for="single-url" class="block text-sm font-medium text-gray-700 mb-2">
                  CSDN 文章链接
                </label>
                <input
                  id="single-url"
                  v-model="singleForm.url"
                  type="url"
                  required
                  placeholder="https://blog.csdn.net/..."
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  :disabled="singleExtracting"
                />
              </div>
              
              <div class="flex items-center">
                <input
                  id="single-force-refresh"
                  v-model="singleForm.forceRefresh"
                  type="checkbox"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  :disabled="singleExtracting"
                />
                <label for="single-force-refresh" class="ml-2 block text-sm text-gray-700">
                  强制刷新（忽略缓存）
                </label>
              </div>
              
              <button
                type="submit"
                :disabled="singleExtracting || !singleForm.url"
                class="w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span v-if="singleExtracting" class="flex items-center justify-center">
                  <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  提取中...
                </span>
                <span v-else>提取文章</span>
              </button>
            </form>
            
            <!-- 提取结果提示 -->
            <div v-if="singleResult" class="mt-4 p-3 rounded-md" :class="{
              'bg-green-50 border border-green-200': singleResult.success,
              'bg-red-50 border border-red-200': !singleResult.success
            }">
              <p class="text-sm" :class="{
                'text-green-700': singleResult.success,
                'text-red-700': !singleResult.success
              }">
                {{ singleResult.success ? '提取成功！' : singleResult.message }}
              </p>
            </div>
            
            <!-- Error message -->
            <div v-if="singleError" class="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p class="text-sm text-red-600">{{ singleError }}</p>
            </div>
          </div>

          <!-- 历史记录区域 -->
          <div class="flex-1 p-6 border-b border-gray-200 overflow-hidden">
            <div class="flex items-center justify-between mb-4">
              <h2 class="text-lg font-semibold text-gray-900">历史记录区域</h2>
              <button
                @click="loadHistory"
                :disabled="loading"
                class="text-sm text-indigo-600 hover:text-indigo-700"
              >
                {{ loading ? '刷新中...' : '刷新' }}
              </button>
            </div>
            
            <!-- 历史记录列表 -->
            <div class="h-full overflow-y-auto">
              <div v-if="loading" class="text-center py-8">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600 mx-auto"></div>
                <p class="text-gray-600 mt-2 text-sm">加载中...</p>
              </div>
              
              <div v-else-if="history.length === 0" class="text-center py-8">
                <p class="text-gray-600 text-sm">暂无提取记录</p>
              </div>
              
              <div v-else class="space-y-2">
                <div
                  v-for="item in history"
                  :key="item.id"
                  @click="showContent(item)"
                  class="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                  :class="{
                    'border-indigo-200 bg-indigo-50': selectedItem?.id === item.id
                  }"
                >
                  <div class="flex items-center">
                    <div
                      class="flex-shrink-0 w-2 h-2 rounded-full mr-2"
                      :class="{
                        'bg-green-500': item.status === 'SUCCESS',
                        'bg-red-500': item.status === 'FAILED',
                        'bg-blue-500': item.status === 'DUPLICATE',
                        'bg-yellow-500': item.status === 'PENDING'
                      }"
                    ></div>
                    <h3 class="text-sm font-medium text-gray-900 truncate flex-1">
                      {{ item.title || '未知标题' }}
                    </h3>
                  </div>
                  
                  <p class="text-xs text-gray-500 mt-1 truncate">
                    {{ formatDate(item.request_time || item.create_time) }}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- 用户设置区域 -->
          <div class="p-6">
            <div class="flex items-center justify-between mb-3">
              <h2 class="text-sm font-semibold text-gray-900">用户设置区域</h2>
              <button
                @click="showTokenDetails = !showTokenDetails"
                class="text-xs text-indigo-600 hover:text-indigo-700"
              >
                {{ showTokenDetails ? '隐藏详情' : '详情设置' }}
              </button>
            </div>
            
            <!-- 基础信息 -->
            <div class="text-xs text-gray-600 space-y-1">
              <p><strong>令牌:</strong> {{ currentToken?.name || '未知' }}</p>
              <p><strong>剩余次数:</strong> {{ remainingUsage }}</p>
              <p><strong>状态:</strong> {{ getTokenStatusText(currentToken?.status) }}</p>
            </div>
            
            <!-- 详细信息（可展开） -->
            <div v-if="showTokenDetails" class="mt-4 pt-4 border-t border-gray-200">
              <div class="text-xs text-gray-600 space-y-1">
                <p><strong>过期时间:</strong> {{ currentToken?.expire_time ? formatDate(currentToken.expire_time) : '无限' }}</p>
                <p><strong>最大使用:</strong> {{ currentToken?.max_usage_count || '无限' }}</p>
                <p><strong>当前使用:</strong> {{ currentToken?.used_count || 0 }}</p>
              </div>
              
              <button
                @click="logout"
                class="mt-4 w-full text-xs bg-red-50 text-red-600 py-2 px-3 rounded-md hover:bg-red-100 transition-colors"
              >
                退出登录
              </button>
            </div>
          </div>
        </div>

        <!-- 右栏 - 文本展示区域 -->
        <div class="flex-1 bg-white p-6">
          <div class="h-full flex flex-col">
            <div class="flex items-center justify-between mb-4">
              <h2 class="text-lg font-semibold text-gray-900">提取结果区域</h2>
              <span class="text-sm text-gray-500">初步涉及及提取md格式的文本</span>
            </div>
            
            <!-- 内容展示 -->
            <div class="flex-1 border border-gray-200 rounded-lg overflow-hidden">
              <div v-if="!selectedItem" class="h-full flex items-center justify-center text-gray-500">
                <div class="text-center">
                  <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  <p class="mt-2 text-sm">选择一篇文章查看内容</p>
                </div>
              </div>
              
              <div v-else class="h-full flex flex-col">
                <!-- 文章标题栏 -->
                <div class="p-4 border-b border-gray-200 bg-gray-50">
                  <h3 class="font-semibold text-gray-900">{{ selectedItem.title }}</h3>
                  <div class="flex items-center text-xs text-gray-500 mt-1 space-x-4">
                    <span v-if="selectedItem.author">作者: {{ selectedItem.author }}</span>
                    <span v-if="selectedItem.complete_time">完成: {{ formatDate(selectedItem.complete_time) }}</span>
                    <span>提取: {{ formatDate(selectedItem.request_time || selectedItem.create_time) }}</span>
                  </div>
                </div>
                
                <!-- 文章内容 -->
                <div class="flex-1 p-4 overflow-y-auto">
                  <div v-if="selectedItem.content" class="prose max-w-none" v-html="renderMarkdown(selectedItem.content)"></div>
                  <div v-else class="text-center text-gray-500 py-8">
                    <p>暂无内容</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { authAPI, extractionAPI } from '@/services/api'
import type { Token, ExtractionRecord } from '@/types'
import { marked } from 'marked'

const authStore = useAuthStore()

// Token management
const tokenInput = ref('')
const validating = ref(false)
const tokenError = ref('')
const currentToken = ref<Token | null>(null)
const showTokenDetails = ref(false)

// Single extraction
const singleForm = reactive({
  url: '',
  forceRefresh: false
})
const singleExtracting = ref(false)
const singleResult = ref<any>(null)
const singleError = ref('')

// History
const history = ref<ExtractionRecord[]>([])
const loading = ref(false)
const selectedItem = ref<ExtractionRecord | null>(null)

// Computed properties
const hasValidToken = computed(() => {
  return !!currentToken.value
})

const remainingUsage = computed(() => {
  if (!currentToken.value) return 'N/A'

  const token = currentToken.value
  if (token.max_usage_count === null || token.max_usage_count === undefined) return '无限'

  const remaining = token.max_usage_count - token.used_count
  return remaining > 0 ? remaining : 0
})

// Methods
const validateAndEnter = async () => {
  if (!tokenInput.value.trim()) return

  validating.value = true
  tokenError.value = ''

  try {
    const response = await authAPI.validateAccessToken(tokenInput.value.trim())

    if (response.data.code === 200 && response.data.result.valid) {
      currentToken.value = response.data.result.token_info
      // Store token in localStorage for persistence
      localStorage.setItem('csdn_token', tokenInput.value.trim())
      // Load initial history
      await loadHistory()
    } else {
      tokenError.value = response.data.result?.error_message || response.data.message || '令牌验证失败'
    }
  } catch (error: any) {
    tokenError.value = error.response?.data?.message || '验证过程中发生错误'
  } finally {
    validating.value = false
  }
}

const logout = () => {
  currentToken.value = null
  tokenInput.value = ''
  localStorage.removeItem('csdn_token')
  history.value = []
  selectedItem.value = null
  showTokenDetails.value = false

  // 重定向到首页
  window.location.href = '/'
}

// Single extraction
const handleSingleExtract = async () => {
  if (!singleForm.url.trim()) return

  singleExtracting.value = true
  singleResult.value = null
  singleError.value = ''

  try {
    const response = await extractionAPI.extract({
      token: localStorage.getItem('csdn_token') || '',
      url: singleForm.url.trim(),
      force_refresh: singleForm.forceRefresh
    })

    if (response.data.code === 200) {
      singleResult.value = response.data.result
      // If successful, add to history and refresh, and show content
      if (response.data.result.success && response.data.result.data) {
        await loadHistory()
        // Auto-select the newly extracted item
        const newItem = {
          id: Date.now(), // temporary ID
          title: response.data.result.data.title,
          author: response.data.result.data.author,
          url: singleForm.url.trim(),
          status: 'SUCCESS',
          request_time: new Date().toISOString(),
          complete_time: new Date().toISOString(),
          content: response.data.result.data.content
        }
        selectedItem.value = newItem
      }
    } else {
      singleError.value = response.data.message || '提取失败'
    }
  } catch (error: any) {
    singleError.value = error.response?.data?.message || '提取过程中发生错误'
  } finally {
    singleExtracting.value = false
  }
}

// History management
const loadHistory = async () => {
  loading.value = true

  try {
    const response = await extractionAPI.getHistory({
      token: localStorage.getItem('csdn_token') || '',
      page: 1,
      size: 50
    })

    if (response.data.code === 200) {
      history.value = response.data.result.items || []
    }
  } catch (error: any) {
    console.error('Load history error:', error)
  } finally {
    loading.value = false
  }
}

const showContent = (item: ExtractionRecord) => {
  selectedItem.value = item
}

// Utility functions
const formatDate = (dateString: string) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleString('zh-CN')
}

const renderMarkdown = (content: string) => {
  if (!content) return ''
  return marked(content)
}

const getTokenStatusText = (status?: number) => {
  const statusMap: { [key: number]: string } = {
    1: '激活',
    2: '已过期',
    3: '次数用完',
    4: '已禁用'
  }
  return statusMap[status || 1] || '未知'
}

// Initialize
onMounted(async () => {
  // Check for stored token
  const storedToken = localStorage.getItem('csdn_token')
  if (storedToken) {
    tokenInput.value = storedToken
    await validateAndEnter()
  }

  // Check URL parameters for token
  const urlParams = new URLSearchParams(window.location.search)
  const urlToken = urlParams.get('token')
  if (urlToken && !hasValidToken.value) {
    tokenInput.value = urlToken
    await validateAndEnter()
  }
})
</script>

<style scoped>
.prose {
  max-width: none;
}

.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
}

.prose p {
  margin-bottom: 1em;
}

.prose ul, .prose ol {
  margin-bottom: 1em;
  padding-left: 1.5em;
}

.prose li {
  margin-bottom: 0.25em;
}

.prose code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
}

.prose pre {
  background-color: #1f2937;
  color: #f9fafb;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1em 0;
}

.prose blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin: 1em 0;
  font-style: italic;
}
</style>
