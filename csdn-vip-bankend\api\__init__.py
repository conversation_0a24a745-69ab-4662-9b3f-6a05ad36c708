from .admin_user import admin_user_router
from .casbin import admin_role_router, admin_casbin_object_router, admin_casbin_action_router, admin_casbin_router
from .login import login_router
from .user import user_router
from .cos import cos_token_router
from .demoGrammar import student_router
from .access_token import access_token_router
from .extraction import extraction_router
from .extraction_core import extraction_core_router
from .image_proxy import image_proxy_router

routers = [
    admin_user_router,
    login_router,
    user_router,
    cos_token_router,
    admin_role_router,
    admin_casbin_object_router,
    admin_casbin_action_router,
    admin_casbin_router,
    student_router,
    access_token_router,
    extraction_router,
    extraction_core_router,
    image_proxy_router,
]

print("这是在api的init")