<template>
  <div class="space-y-6">
    <!-- Page header -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">提取历史</h1>
      <p class="text-gray-600">查看所有文章提取记录</p>
    </div>

    <!-- Filters -->
    <div class="card">
      <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
          <select v-model="filters.status" @change="loadHistory" class="input">
            <option value="">全部状态</option>
            <option value="SUCCESS">成功</option>
            <option value="FAILED">失败</option>
            <option value="DUPLICATE">重复</option>
            <option value="PENDING">处理中</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">开始日期</label>
          <input
            v-model="filters.startDate"
            type="date"
            @change="loadHistory"
            class="input"
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">结束日期</label>
          <input
            v-model="filters.endDate"
            type="date"
            @change="loadHistory"
            class="input"
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">搜索</label>
          <input
            v-model="filters.search"
            @input="debounceSearch"
            type="text"
            placeholder="搜索标题或URL..."
            class="input"
          />
        </div>
        
        <div class="flex items-end">
          <button
            @click="loadHistory"
            :disabled="loading"
            class="btn btn-secondary w-full"
          >
            {{ loading ? '查询中...' : '刷新' }}
          </button>
        </div>
      </div>
    </div>

    <!-- History table -->
    <div class="card">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                文章信息
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                令牌信息
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                提取时间
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-if="loading">
              <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                <div class="flex items-center justify-center">
                  <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-primary-600 mr-2"></div>
                  加载中...
                </div>
              </td>
            </tr>
            
            <tr v-else-if="history.length === 0">
              <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                暂无提取记录
              </td>
            </tr>
            
            <tr v-else v-for="item in history" :key="item.id" class="hover:bg-gray-50">
              <td class="px-6 py-4">
                <div class="max-w-xs">
                  <div class="text-sm font-medium text-gray-900 truncate">
                    {{ item.title || '未知标题' }}
                  </div>
                  <div class="text-sm text-gray-500 truncate">{{ item.url }}</div>
                  <div v-if="item.author" class="text-xs text-gray-400">作者: {{ item.author }}</div>
                </div>
              </td>
              
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="space-y-1">
                  <span
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    :class="{
                      'bg-green-100 text-green-800': item.status === 3,
                      'bg-red-100 text-red-800': item.status === 4,
                      'bg-blue-100 text-blue-800': item.status === 5,
                      'bg-yellow-100 text-yellow-800': item.status === 1 || item.status === 2
                    }"
                  >
                    {{ getStatusText(item.status) }}
                  </span>
                  <div v-if="item.error_message" class="text-xs text-red-600 max-w-xs truncate">
                    {{ item.error_message }}
                  </div>
                </div>
              </td>
              
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div>
                  <div class="font-medium">令牌ID: {{ item.access_token_id }}</div>
                  <div class="text-xs text-gray-500">IP: {{ item.client_ip || '未知' }}</div>
                </div>
              </td>
              
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <div>
                  <div>请求: {{ formatDate(item.request_time) }}</div>
                  <div v-if="item.complete_time" class="text-xs">
                    完成: {{ formatDate(item.complete_time) }}
                  </div>
                </div>
              </td>

              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button
                  v-if="item.status === 3 && item.content_length > 0"
                  @click="showContent(item)"
                  class="text-primary-600 hover:text-primary-900"
                >
                  查看内容
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <!-- Pagination -->
      <div v-if="pagination.pages > 1" class="px-6 py-4 border-t border-gray-200">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-700">
            显示第 {{ (pagination.page - 1) * pagination.size + 1 }} - 
            {{ Math.min(pagination.page * pagination.size, pagination.total) }} 条，
            共 {{ pagination.total }} 条记录
          </div>
          
          <div class="flex space-x-2">
            <button
              @click="changePage(pagination.page - 1)"
              :disabled="pagination.page <= 1"
              class="btn btn-secondary text-sm"
            >
              上一页
            </button>
            
            <button
              @click="changePage(pagination.page + 1)"
              :disabled="pagination.page >= pagination.pages"
              class="btn btn-secondary text-sm"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Content modal -->
    <div
      v-if="selectedItem"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
      @click="selectedItem = null"
    >
      <div
        class="bg-white rounded-lg max-w-4xl w-full max-h-[80vh] overflow-hidden"
        @click.stop
      >
        <div class="p-6 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900">
              {{ selectedItem.title }}
            </h3>
            <button
              @click="selectedItem = null"
              class="text-gray-400 hover:text-gray-600"
            >
              <span class="sr-only">关闭</span>
              ✕
            </button>
          </div>
        </div>
        
        <div class="p-6 overflow-y-auto max-h-[60vh]">
          <div class="prose max-w-none" v-html="selectedItem.content"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { extractionAPI } from '@/services/api'
import type { ExtractionRecord, ExtractionStatus } from '@/types'

const loading = ref(false)
const history = ref<ExtractionRecord[]>([])
const selectedItem = ref<ExtractionRecord | null>(null)

const filters = reactive({
  status: '' as ExtractionStatus | '',
  startDate: '',
  endDate: '',
  search: ''
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0,
  pages: 0
})

let searchTimeout: NodeJS.Timeout

const loadHistory = async () => {
  loading.value = true

  try {
    const response = await extractionAPI.adminList({
      page: pagination.page,
      size: pagination.size,
      status: filters.status || undefined,
      start_date: filters.startDate || undefined,
      end_date: filters.endDate || undefined,
      search: filters.search || undefined
    })

    const result = response.data.result
    history.value = result.items || []
    pagination.total = result.total || 0
    pagination.pages = result.pages || 0
  } catch (error) {
    console.error('Failed to load history:', error)
  } finally {
    loading.value = false
  }
}

const debounceSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    pagination.page = 1
    loadHistory()
  }, 500)
}

const changePage = (page: number) => {
  pagination.page = page
  loadHistory()
}

const showContent = (item: ExtractionRecord) => {
  selectedItem.value = item
}

const getStatusText = (status: number) => {
  const statusMap: { [key: number]: string } = {
    1: '等待处理',
    2: '处理中',
    3: '成功',
    4: '失败',
    5: '重复'
  }
  return statusMap[status] || '未知状态'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  loadHistory()
})
</script>
