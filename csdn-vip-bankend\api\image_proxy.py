from typing import Optional
from fastapi import APIRouter, HTTPException, Query, Request, Depends
from fastapi.responses import Response
import httpx
import hashlib
import os
from pathlib import Path
import asyncio
import time
from core.response import ResultResponse
from core.exce.base import UserError
from models import AccessToken, AdminUser
from models.token_usage_log import TokenUsageLog, UsageType, UsageStatus
from utils.datetime.tztime import get_sh_tz_time_aware
from core.auth.base import get_current_active_user

image_proxy_router = APIRouter(
    prefix="/image-proxy",
    tags=["图片代理"],
)

# 图片缓存目录
CACHE_DIR = Path("cache/images")
CACHE_DIR.mkdir(parents=True, exist_ok=True)

# 支持的图片格式
SUPPORTED_FORMATS = {
    'jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg'
}

async def validate_image_proxy_token(token: str, client_ip: str, request_url: str) -> AccessToken:
    """验证图片代理token - 只验证有效性，不计使用限制和IP限制"""
    # 查找token
    access_token = await AccessToken.get_or_none(token=token)
    if not access_token or access_token.is_deleted:
        await TokenUsageLog.log_usage(
            token_id=0,  # 无效token时使用0
            usage_type=UsageType.IMAGE_PROXY,
            status=UsageStatus.FAILED,
            client_ip=client_ip,
            request_url=request_url,
            error_code="TOKEN_NOT_FOUND",
            error_message="访问令牌不存在"
        )
        raise UserError('访问令牌不存在')

    # 检查token有效性（只检查过期时间和状态，不检查使用次数）
    if access_token.status != 1:  # TokenStatus.ACTIVE = 1
        await TokenUsageLog.log_usage(
            token_id=access_token.id,
            usage_type=UsageType.IMAGE_PROXY,
            status=UsageStatus.FAILED,
            client_ip=client_ip,
            request_url=request_url,
            error_code="TOKEN_INACTIVE",
            error_message="访问令牌未激活"
        )
        raise UserError('访问令牌未激活')

    # 只检查过期时间，不检查使用次数限制
    if access_token.expire_time and get_sh_tz_time_aware() > access_token.expire_time:
        await TokenUsageLog.log_usage(
            token_id=access_token.id,
            usage_type=UsageType.IMAGE_PROXY,
            status=UsageStatus.FAILED,
            client_ip=client_ip,
            request_url=request_url,
            error_code="TOKEN_EXPIRED",
            error_message="访问令牌已过期"
        )
        raise UserError('访问令牌已过期')

    # 图片代理不检查IP绑定和使用限制，直接返回token
    return access_token

def get_cache_path(url: str) -> Path:
    """根据URL生成缓存文件路径"""
    url_hash = hashlib.md5(url.encode()).hexdigest()
    return CACHE_DIR / f"{url_hash}"

def get_content_type(url: str) -> str:
    """根据URL推断内容类型"""
    ext = url.lower().split('.')[-1].split('?')[0]
    content_types = {
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg',
        'png': 'image/png',
        'gif': 'image/gif',
        'webp': 'image/webp',
        'bmp': 'image/bmp',
        'svg': 'image/svg+xml'
    }
    return content_types.get(ext, 'image/jpeg')

async def download_image(url: str) -> tuple[bytes, str]:
    """下载图片并返回内容和内容类型"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://blog.csdn.net/',
        'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Cache-Control': 'no-cache',
    }
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        response = await client.get(url, headers=headers, follow_redirects=True)
        response.raise_for_status()
        
        content_type = response.headers.get('content-type', get_content_type(url))
        return response.content, content_type

@image_proxy_router.get(
    "",
    summary="图片代理",
    description="代理获取CSDN等网站的图片，解决跨域问题，需要有效的访问令牌"
)
async def proxy_image(
    request: Request,
    url: str = Query(..., description="图片URL"),
    token: str = Query(..., description="访问令牌"),
    cache: bool = Query(True, description="是否使用缓存")
):
    """代理获取图片"""
    start_time = time.time()
    client_ip = request.client.host
    user_agent = request.headers.get('user-agent')
    request_url = str(request.url)

    if not url:
        raise HTTPException(status_code=400, detail="URL参数不能为空")

    # 验证token
    try:
        access_token = await validate_image_proxy_token(token, client_ip, request_url)
    except UserError as e:
        raise HTTPException(status_code=401, detail=str(e))

    # 检查URL是否为图片
    url_lower = url.lower()
    if not any(fmt in url_lower for fmt in SUPPORTED_FORMATS):
        # 记录失败日志
        await TokenUsageLog.log_usage(
            token_id=access_token.id,
            usage_type=UsageType.IMAGE_PROXY,
            status=UsageStatus.FAILED,
            client_ip=client_ip,
            user_agent=user_agent,
            request_url=request_url,
            response_time=time.time() - start_time,
            error_code="UNSUPPORTED_FORMAT",
            error_message="不支持的图片格式"
        )
        raise HTTPException(status_code=400, detail="不支持的图片格式")
    
    cache_path = get_cache_path(url)
    
    # 如果启用缓存且缓存文件存在
    if cache and cache_path.exists():
        try:
            content = cache_path.read_bytes()
            content_type = get_content_type(url)

            # 记录成功日志（图片代理不计使用次数）
            await TokenUsageLog.log_usage(
                token_id=access_token.id,
                usage_type=UsageType.IMAGE_PROXY,
                status=UsageStatus.SUCCESS,
                client_ip=client_ip,
                user_agent=user_agent,
                request_url=request_url,
                response_time=time.time() - start_time,
                extra_data={"cache_hit": True, "image_size": len(content)}
            )

            return Response(
                content=content,
                media_type=content_type,
                headers={
                    "Cache-Control": "public, max-age=86400",  # 缓存1天
                    "X-Cache": "HIT"
                }
            )
        except Exception:
            # 缓存文件损坏，删除并重新下载
            cache_path.unlink(missing_ok=True)
    
    try:
        # 下载图片
        content, content_type = await download_image(url)
        
        # 保存到缓存
        if cache:
            try:
                cache_path.write_bytes(content)
            except Exception as e:
                # 缓存失败不影响返回结果
                print(f"缓存图片失败: {e}")

        # 记录成功日志（图片代理不计使用次数）
        await TokenUsageLog.log_usage(
            token_id=access_token.id,
            usage_type=UsageType.IMAGE_PROXY,
            status=UsageStatus.SUCCESS,
            client_ip=client_ip,
            user_agent=user_agent,
            request_url=request_url,
            response_time=time.time() - start_time,
            extra_data={"cache_hit": False, "image_size": len(content)}
        )

        return Response(
            content=content,
            media_type=content_type,
            headers={
                "Cache-Control": "public, max-age=86400",  # 缓存1天
                "X-Cache": "MISS"
            }
        )
        
    except httpx.HTTPStatusError as e:
        # 记录失败日志
        await TokenUsageLog.log_usage(
            token_id=access_token.id,
            usage_type=UsageType.IMAGE_PROXY,
            status=UsageStatus.FAILED,
            client_ip=client_ip,
            user_agent=user_agent,
            request_url=request_url,
            response_time=time.time() - start_time,
            error_code="HTTP_ERROR",
            error_message=f"HTTP错误: {e.response.status_code}"
        )
        raise HTTPException(status_code=e.response.status_code, detail=f"获取图片失败: {e}")
    except httpx.TimeoutException:
        # 记录失败日志
        await TokenUsageLog.log_usage(
            token_id=access_token.id,
            usage_type=UsageType.IMAGE_PROXY,
            status=UsageStatus.FAILED,
            client_ip=client_ip,
            user_agent=user_agent,
            request_url=request_url,
            response_time=time.time() - start_time,
            error_code="TIMEOUT",
            error_message="请求超时"
        )
        raise HTTPException(status_code=408, detail="请求超时")
    except Exception as e:
        # 记录失败日志
        await TokenUsageLog.log_usage(
            token_id=access_token.id,
            usage_type=UsageType.IMAGE_PROXY,
            status=UsageStatus.FAILED,
            client_ip=client_ip,
            user_agent=user_agent,
            request_url=request_url,
            response_time=time.time() - start_time,
            error_code="SERVER_ERROR",
            error_message=f"服务器错误: {str(e)}"
        )
        raise HTTPException(status_code=500, detail=f"服务器错误: {str(e)}")

@image_proxy_router.delete(
    "/cache",
    summary="清理图片缓存",
    description="清理所有缓存的图片文件，需要管理员权限"
)
async def clear_image_cache(
    current_user: AdminUser = Depends(get_current_active_user)
):
    """清理图片缓存"""
    try:
        deleted_count = 0
        for cache_file in CACHE_DIR.glob("*"):
            if cache_file.is_file():
                cache_file.unlink()
                deleted_count += 1

        return ResultResponse[dict](
            result={
                "message": f"成功清理 {deleted_count} 个缓存文件",
                "deleted_count": deleted_count
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清理缓存失败: {str(e)}")

@image_proxy_router.get(
    "/cache/info",
    summary="获取缓存信息",
    description="获取图片缓存的统计信息，需要管理员权限"
)
async def get_cache_info(
    current_user: AdminUser = Depends(get_current_active_user)
):
    """获取缓存信息"""
    try:
        cache_files = list(CACHE_DIR.glob("*"))
        file_count = len([f for f in cache_files if f.is_file()])
        
        total_size = 0
        for cache_file in cache_files:
            if cache_file.is_file():
                total_size += cache_file.stat().st_size
        
        return ResultResponse[dict](
            result={
                "cache_dir": str(CACHE_DIR),
                "file_count": file_count,
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size / 1024 / 1024, 2)
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取缓存信息失败: {str(e)}")
